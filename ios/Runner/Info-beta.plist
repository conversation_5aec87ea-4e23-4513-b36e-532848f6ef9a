<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>$(INFOPLIST_KEY_CFBundleDisplayName)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>App Link</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>card3</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>FLTEnableWideGamut</key>
	<true/>
	<key>FlutterDeepLinkingEnabled</key>
	<false/>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>tg</string>
		<string>twitter</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NFCReaderUsageDescription</key>
	<string>Read NFC Tags</string>
	<key>NSCameraUsageDescription</key>
	<string>Request for the QR scanning feature</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Request for the audio recording feature</string>
	<key>NSUserNotificationsUsageDescription</key>
	<string>This app needs notification access to receive push messages</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
		<string>fetch</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>com.apple.developer.nfc.readersession.felica.systemcodes</key>
	<array>
		<string>0000</string>
		<string>8005</string>
		<string>8008</string>
		<string>0003</string>
		<string>fe00</string>
		<string>90b7</string>
		<string>927a</string>
		<string>86a7</string>
	</array>
	<key>com.apple.developer.nfc.readersession.iso7816.select-identifiers</key>
	<array>
		<string>A0000002471001</string>
		<string>A000000003101001</string>
		<string>A000000003101002</string>
		<string>A0000000041010</string>
		<string>A0000000042010</string>
		<string>A0000000044010</string>
		<string>44464D46412E44466172653234313031</string>
		<string>D2760000850100</string>
		<string>D2760000850101</string>
		<string>00000000000000</string>
	</array>
</dict>
</plist>
