// GENERATED CODE - DO NOT MODIFY MANUALLY
// **************************************************************************
// Auto generated by https://github.com/fluttercandies/ff_annotation_route
// **************************************************************************
// fast mode: true
// **************************************************************************
// ignore_for_file: duplicate_import,implementation_imports,library_private_types_in_public_api,multiple_combinators,prefer_const_literals_to_create_immutables,unintended_html_in_doc_comment,unnecessary_import,unused_import,unused_local_variable,unused_shown_name,unnecessary_library_name,unnecessary_library_directive
import 'package:flutter/foundation.dart';

import '/feat/scan/uni_qr.dart';
import '/models/business.dart';
import '/models/card.dart' show EthccProfile, Social, SocialPlatform;
import '/models/card.dart' show Social, SocialPlatform;
import '/models/user.dart' show UserFromRelation, UserFromRelationType;
import '/models/user.dart' show UserInfo;

/// The routeNames auto generated by https://github.com/fluttercandies/ff_annotation_route
const List<String> routeNames = <String>[
  '/',
  '/ai/generate/image',
  '/customize',
  '/customize/prints',
  '/fun/connection',
  '/fun/point_record',
  '/fun/referral',
  '/home',
  '/login',
  '/login/email',
  '/notification',
  '/scan',
  '/setting/about',
  '/setting/account',
  '/setting/mode',
  '/settings/env',
  '/share',
  '/social/edit-profile',
  '/social/platform',
  '/social/profile',
  '/wallet/authenticate',
  '/wallet/management',
  '/wallet/portfolio',
  '/wallet/send',
  '/webview',
];

/// The routes auto generated by https://github.com/fluttercandies/ff_annotation_route
class Routes {
  const Routes._();

  /// '/'
  ///
  /// [name] : '/'
  static const _Root root = _Root();

  /// '/ai/generate/image'
  ///
  /// [name] : '/ai/generate/image'
  ///
  /// [constructors] :
  ///
  /// AIGenerateImagePage : [String? taskId, String? generatedUrl, String? filePath, ImageAIStyle? style]
  static const _AiGenerateImage aiGenerateImage = _AiGenerateImage();

  /// '/customize'
  ///
  /// [name] : '/customize'
  ///
  /// [constructors] :
  ///
  /// CustomizePage : [String? code]
  static const _Customize customize = _Customize();

  /// '/customize/prints'
  ///
  /// [name] : '/customize/prints'
  static const _CustomizePrints customizePrints = _CustomizePrints();

  /// '/fun/connection'
  ///
  /// [name] : '/fun/connection'
  ///
  /// [constructors] :
  ///
  /// ConnectionPage : [UserFromRelationType? type]
  static const _FunConnection funConnection = _FunConnection();

  /// '/fun/point_record'
  ///
  /// [name] : '/fun/point_record'
  static const _FunPointRecord funPointRecord = _FunPointRecord();

  /// '/fun/referral'
  ///
  /// [name] : '/fun/referral'
  static const _FunReferral funReferral = _FunReferral();

  /// '/home'
  ///
  /// [name] : '/home'
  static const _Home home = _Home();

  /// '/login'
  ///
  /// [name] : '/login'
  static const _Login login = _Login();

  /// '/login/email'
  ///
  /// [name] : '/login/email'
  static const _LoginEmail loginEmail = _LoginEmail();

  /// '/notification'
  ///
  /// [name] : '/notification'
  static const _Notification notification = _Notification();

  /// '/scan'
  ///
  /// [name] : '/scan'
  ///
  /// [constructors] :
  ///
  /// QRScan : [ScanManager(required) manager]
  static const _Scan scan = _Scan();

  /// '/setting/about'
  ///
  /// [name] : '/setting/about'
  static const _SettingAbout settingAbout = _SettingAbout();

  /// '/setting/account'
  ///
  /// [name] : '/setting/account'
  static const _SettingAccount settingAccount = _SettingAccount();

  /// '/setting/mode'
  ///
  /// [name] : '/setting/mode'
  static const _SettingMode settingMode = _SettingMode();

  /// '/settings/env'
  ///
  /// [name] : '/settings/env'
  static const _SettingsEnv settingsEnv = _SettingsEnv();

  /// '/share'
  ///
  /// [name] : '/share'
  static const _Share share = _Share();

  /// '/social/edit-profile'
  ///
  /// [name] : '/social/edit-profile'
  ///
  /// [constructors] :
  ///
  /// SocialEditProfilePage : [String? pendingAvatarUrl]
  static const _SocialEditProfile socialEditProfile = _SocialEditProfile();

  /// '/social/platform'
  ///
  /// [name] : '/social/platform'
  ///
  /// [constructors] :
  ///
  /// SocialPlatformPage : [SocialPlatform(required) platform, String? action, Social? social, String? currentHandle]
  static const _SocialPlatform socialPlatform = _SocialPlatform();

  /// '/social/profile'
  ///
  /// [name] : '/social/profile'
  ///
  /// [constructors] :
  ///
  /// SocialProfilePage : [String(required) code, UserInfo? profile]
  static const _SocialProfile socialProfile = _SocialProfile();

  /// '/wallet/authenticate'
  ///
  /// [name] : '/wallet/authenticate'
  static const _WalletAuthenticate walletAuthenticate = _WalletAuthenticate();

  /// '/wallet/management'
  ///
  /// [name] : '/wallet/management'
  static const _WalletManagement walletManagement = _WalletManagement();

  /// '/wallet/portfolio'
  ///
  /// [name] : '/wallet/portfolio'
  static const _WalletPortfolio walletPortfolio = _WalletPortfolio();

  /// '/wallet/send'
  ///
  /// [name] : '/wallet/send'
  ///
  /// [constructors] :
  ///
  /// SendPage : [IToken(required) token]
  static const _WalletSend walletSend = _WalletSend();

  /// '/webview'
  ///
  /// [name] : '/webview'
  ///
  /// [constructors] :
  ///
  /// WebViewPage : [String(required) url, String? title]
  static const _Webview webview = _Webview();
}

class _Root {
  const _Root();

  String get name => '/';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _AiGenerateImage {
  const _AiGenerateImage();

  String get name => '/ai/generate/image';

  Map<String, dynamic> d({
    Key? key,
    String? taskId,
    String? generatedUrl,
    String? filePath,
    ImageAIStyle? style,
  }) =>
      <String, dynamic>{
        'key': key,
        'taskId': taskId,
        'generatedUrl': generatedUrl,
        'filePath': filePath,
        'style': style,
      };

  @override
  String toString() => name;
}

class _Customize {
  const _Customize();

  String get name => '/customize';

  Map<String, dynamic> d({
    Key? key,
    String? code,
  }) =>
      <String, dynamic>{
        'key': key,
        'code': code,
      };

  @override
  String toString() => name;
}

class _CustomizePrints {
  const _CustomizePrints();

  String get name => '/customize/prints';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _FunConnection {
  const _FunConnection();

  String get name => '/fun/connection';

  Map<String, dynamic> d({
    Key? key,
    UserFromRelationType? type,
  }) =>
      <String, dynamic>{
        'key': key,
        'type': type,
      };

  @override
  String toString() => name;
}

class _FunPointRecord {
  const _FunPointRecord();

  String get name => '/fun/point_record';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _FunReferral {
  const _FunReferral();

  String get name => '/fun/referral';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _Home {
  const _Home();

  String get name => '/home';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _Login {
  const _Login();

  String get name => '/login';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _LoginEmail {
  const _LoginEmail();

  String get name => '/login/email';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _Notification {
  const _Notification();

  String get name => '/notification';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _Scan {
  const _Scan();

  String get name => '/scan';

  Map<String, dynamic> d({
    Key? key,
    required ScanManager manager,
  }) =>
      <String, dynamic>{
        'key': key,
        'manager': manager,
      };

  @override
  String toString() => name;
}

class _SettingAbout {
  const _SettingAbout();

  String get name => '/setting/about';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _SettingAccount {
  const _SettingAccount();

  String get name => '/setting/account';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _SettingMode {
  const _SettingMode();

  String get name => '/setting/mode';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _SettingsEnv {
  const _SettingsEnv();

  String get name => '/settings/env';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _Share {
  const _Share();

  String get name => '/share';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _SocialEditProfile {
  const _SocialEditProfile();

  String get name => '/social/edit-profile';

  Map<String, dynamic> d({
    Key? key,
    String? pendingAvatarUrl,
  }) =>
      <String, dynamic>{
        'key': key,
        'pendingAvatarUrl': pendingAvatarUrl,
      };

  @override
  String toString() => name;
}

class _SocialPlatform {
  const _SocialPlatform();

  String get name => '/social/platform';

  Map<String, dynamic> d({
    Key? key,
    required SocialPlatform platform,
    String? action,
    Social? social,
    String? currentHandle,
  }) =>
      <String, dynamic>{
        'key': key,
        'platform': platform,
        'action': action,
        'social': social,
        'currentHandle': currentHandle,
      };

  @override
  String toString() => name;
}

class _SocialProfile {
  const _SocialProfile();

  String get name => '/social/profile';

  Map<String, dynamic> d({
    Key? key,
    required String code,
    UserInfo? profile,
  }) =>
      <String, dynamic>{
        'key': key,
        'code': code,
        'profile': profile,
      };

  @override
  String toString() => name;
}

class _WalletAuthenticate {
  const _WalletAuthenticate();

  String get name => '/wallet/authenticate';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _WalletManagement {
  const _WalletManagement();

  String get name => '/wallet/management';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _WalletPortfolio {
  const _WalletPortfolio();

  String get name => '/wallet/portfolio';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _WalletSend {
  const _WalletSend();

  String get name => '/wallet/send';

  Map<String, dynamic> d({
    Key? key,
    required IToken token,
  }) =>
      <String, dynamic>{
        'key': key,
        'token': token,
      };

  @override
  String toString() => name;
}

class _Webview {
  const _Webview();

  String get name => '/webview';

  Map<String, dynamic> d({
    Key? key,
    required String url,
    String? title,
  }) =>
      <String, dynamic>{
        'key': key,
        'url': url,
        'title': title,
      };

  @override
  String toString() => name;
}
