// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'config.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Config _$ConfigFromJson(Map json) => _Config(
  chainTokenSymbolFilters: (json['chainTokenFilters'] as Map).map(
    (k, e) => MapEntry(
      k as String,
      (e as List<dynamic>).map((e) => e as String).toList(),
    ),
  ),
  chainFilters: (json['chainFilters'] as List<dynamic>)
      .map((e) => (e as num).toInt())
      .toList(),
  ethccEventIds:
      (json['ethccEventIds'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList() ??
      const [],
  defaultMode:
      $enumDecodeNullable(_$ProfileModeEnumMap, json['defaultMode']) ??
      ProfileMode.ETHCC,
  activationGuide: json['activationGuide'] as bool? ?? false,
  events: (json['events'] as List<dynamic>)
      .map((e) => EventItem.fromJson(Map<String, dynamic>.from(e as Map)))
      .toList(),
  useOkxApi: json['okx'] as bool? ?? true,
);

Map<String, dynamic> _$ConfigToJson(_Config instance) => <String, dynamic>{
  'chainTokenFilters': instance.chainTokenSymbolFilters,
  'chainFilters': instance.chainFilters,
  'ethccEventIds': instance.ethccEventIds,
  'defaultMode': _$ProfileModeEnumMap[instance.defaultMode]!,
  'activationGuide': instance.activationGuide,
  'events': instance.events.map((e) => e.toJson()).toList(),
  'okx': instance.useOkxApi,
};

const _$ProfileModeEnumMap = {
  ProfileMode.DEFAULT: 'DEFAULT',
  ProfileMode.ETHCC: 'ETHCC',
  ProfileMode.EMPTY: '',
};
