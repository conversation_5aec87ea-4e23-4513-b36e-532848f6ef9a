import 'dart:convert' show jsonEncode;
import 'dart:io' as io show HttpHeaders;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:me_extensions/me_extensions.dart';
import 'package:me_misc/me_misc.dart';
import 'package:me_utils/me_utils.dart' show DeviceUtil, LogUtil;
import 'package:mime/mime.dart' show lookupMimeType;
import 'package:path/path.dart' as p;

import '/constants/envs.dart' show envApiUrlService;
import '/internals/box.dart' show BoxService;
import '/models/business.dart';
import '/models/card.dart';
import '/models/user.dart';
import '/provider/api.dart' hide apiServiceProvider;
import '/routes/card3_routes.dart' show Routes;
import '_path.dart';

final class ServiceApi {
  ServiceApi(this.ref);

  final Ref ref;

  late final http = ref
      .read(httpProvider)
      .clone(
        options: BaseOptions(
          baseUrl: '$envApiUrlService/active',
        ),
      );

  Future<String> getRefreshJWT({CancelToken? cancelToken}) async {
    final res = await http
        .get(
          ApiPath.service.user.refreshToken,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => v as String);
    return rep.data;
  }

  Future<void> emailSendCode({
    required String email,
    String? referral,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .post(
          ApiPath.service.email.sendCode,
          data: <String, dynamic>{
            'email': email,
            'referralCode': referral,
          },
          options: Options(
            headers: <String, dynamic>{
              io.HttpHeaders.authorizationHeader: '', // Anonymous.
            },
          ),
        )
        .retry();
    Rep.fromJson(res.data, (v) => v);
  }

  Future<String> emailLogin({
    required String email,
    required String code,
    String? referral,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .post(
          ApiPath.service.email.login,
          data: <String, dynamic>{
            'email': email,
            'code': code,
            'referralCode': referral,
          },
          options: Options(
            headers: <String, dynamic>{
              io.HttpHeaders.authorizationHeader: '', // Anonymous.
            },
          ),
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => v as String);
    return rep.data;
  }

  Future<String> privyLogin({
    required String token,
    String? referral,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .post(
          ApiPath.service.privy.login,
          data: <String, dynamic>{
            'token': token,
            'referralCode': referral,
          },
          options: Options(
            headers: <String, dynamic>{
              io.HttpHeaders.authorizationHeader: '', // Anonymous.
            },
          ),
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => v as String);
    return rep.data;
  }

  Future<UserInfo> getSelfUserInfo({
    String? token,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.user.info,
          cancelToken: cancelToken,
          options: Options(
            headers: {
              if (token != null) io.HttpHeaders.authorizationHeader: 'Bearer $token',
            },
          ),
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => UserInfo.fromJson(v.asJson()),
    );
    return rep.data;
  }

  Future<Map<String, dynamic>> updateUserInfo({
    String? avatar,
    String? name,
    String? title,
    String? company,
    String? profileMode,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .put(
          ApiPath.service.user.info,
          data: <String, Object>{
            'avatar': ?avatar,
            'name': ?name,
            'title': ?title,
            'company': ?company,
            'profileMode': ?profileMode,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => v.asJson());
    return rep.data;
  }

  Future<String> deleteUser({
    CancelToken? cancelToken,
  }) async {
    final res = await http.delete(
      ApiPath.service.user.delete,
      cancelToken: cancelToken,
    );
    final rep = Rep.fromJson(res.data, (v) => v as String);
    return rep.data;
  }

  Future<UserSettingsRequest> getUserSettings({
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.user.settings,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => UserSettingsRequest.fromJson(v.asJson()));
    return rep.data;
  }

  Future<UserSettingsRequest> updateSettings(
    UserSettingsRequest request, {
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.put(
        ApiPath.service.user.settings,
        data: request.toJson(),
        cancelToken: cancelToken,
      ),
    );
    final rep = Rep.fromJson(res.data, (v) => UserSettingsRequest.fromJson(v.asJson()));
    return rep.data;
  }

  Future<List<CardInfo>> getMyCards({
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.user.cards,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = ListRep.fromJson(
      res.data,
      (v) => CardInfo.fromJson(v.asJson()),
    );
    return rep.list;
  }

  Future<CardInfo> getCard({
    required String cardCode,
    String? ctr,
    String? cmac,
    String? activateCode,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .post(
          ApiPath.service.wallet.getCard,
          data: <String, dynamic>{
            'cardCode': cardCode,
            'ctr': ?ctr,
            'cmac': ?cmac,
            'activeCode': ?activateCode,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => CardInfo.fromJson(v.asJson()));
    return rep.data;
  }

  Future<CardInfoBasic> getCardInfoBasic({
    required Uri formalizedUri,
    CancelToken? cancelToken,
  }) async {
    final res = await ref
        .read(httpProvider)
        .getUri(
          formalizedUri,
          options: Options(headers: {'APP-UUID': DeviceUtil.deviceUuid}),
        )
        .retry(retryTimes: 2);
    final rep = Rep.fromJson(
      res.data,
      (v) {
        if (v is! Map<String, dynamic>) {
          LogUtil.e(
            'Invalid card info basic response: $v\n[URI]: $formalizedUri',
            stackTrace: StackTrace.current,
          );
        }
        return CardInfoBasic.fromJson(v.asJson());
      },
    );
    return rep.data;
  }

  Future<String> activeCardByNfc({
    required String uid,
    String? ctr,
    String? cmac,
    String? activateCode,
    CancelToken? cancelToken,
  }) async {
    final data = <String, dynamic>{};

    // 如果有ctr和cmac，使用NFC方式激活
    if (ctr != null && ctr.isNotEmpty && cmac != null && cmac.isNotEmpty) {
      data['uid'] = uid;
      data['ctr'] = ctr;
      data['cmac'] = cmac;
    }
    // 否则使用cardCode方式激活
    else {
      data['cardCode'] = uid;
      if (activateCode case final code? when code.isNotEmpty) {
        data['activeCode'] = code;
      }
    }

    final res = await http
        .post(
          ApiPath.service.user.activateCard,
          data: data,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => v as String);
    return rep.data;
  }

  Future<List<Social>> socialQuery({
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.user.socials,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = ListRep.fromJson(res.data, (v) => Social.fromJson(v.asJson()));
    return rep.list;
  }

  Future<Social> socialAdd({
    required String handleName,
    required String platformName,
    String? platformUrl,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .post(
          ApiPath.service.user.socials,
          data: <String, dynamic>{
            'handleName': handleName,
            'platformUrl': platformUrl,
            'platformName': platformName,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => Social.fromJson(v.asJson()));
    return rep.data;
  }

  Future<Social> socialUpdate({
    required String socialId,
    required String handleName,
    required String platformName,
    String? platformUrl,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .put(
          ApiPath.service.user.socialUpdate(socialId: socialId),
          data: <String, dynamic>{
            'platformName': platformName.trim(),
            'handleName': handleName.trim(),
            'platformUrl': platformUrl?.trim(),
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => Social.fromJson(v.asJson()));
    return rep.data;
  }

  Future<String> socialDelete({
    required int socialId,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .delete(
          ApiPath.service.user.socialDel(socialId: socialId.toString()),
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => v as String);
    return rep.data;
  }

  Future<String> socialsReorder({
    required Map<String, int> idInOrders,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .put(
          ApiPath.service.user.socialSort,
          data: <String, dynamic>{
            'sorts': idInOrders,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => v as String);
    return rep.data;
  }

  Future<Paged<Message>> listMessages({
    required int page,
    int size = 20,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.user.listMessages,
          queryParameters: <String, dynamic>{
            'pageNum': page,
            'pageSize': 20,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => Paged.fromJson(
        v.asJson(),
        (v) => Message.fromJson(v.asJson()),
      ),
    );
    return rep.data;
  }

  Future<Paged<Point>> getPoints({
    required int pageNum,
    required int pageSize,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.user.points,
          queryParameters: <String, dynamic>{
            'pageNum': pageNum,
            'pageSize': pageSize,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => Paged.fromJson(
        v.asJson(),
        (v) => Point.fromJson(v.asJson()),
      ),
    );
    return rep.data;
  }

  Future<CoverInfo> getCoverInfo({
    required String code,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.cover.getInfo,
          queryParameters: <String, String>{'code': code},
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => CoverInfo.fromJson(v.asJson()));
    return rep.data;
  }

  Future<CreateCardCoverResponse> createCardCover({
    required String code,
    required String username,
    String? fileContent,
    String? title,
    String? company,
    String? eventId,
    String? previewContent,
    CancelToken? cancelToken,
  }) async {
    final res = await http.post(
      ApiPath.service.cover.create,
      data: <String, dynamic>{
        'code': code,
        'username': username,
        'fileContent': fileContent,
        'title': title,
        'company': company,
        'eventId': eventId,
        'previewContent': previewContent,
      },
      cancelToken: cancelToken,
    );
    final rep = Rep.fromJson(res.data, (v) => CreateCardCoverResponse.fromJson(v.asJson()));
    return rep.data;
  }

  Future<Paged<CustomizeCardInfo>> getCustomizeCardOrders({
    required int page,
    required int size,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.cover.orders,
          queryParameters: <String, dynamic>{
            'pageNum': page,
            'pageSize': size,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => Paged.fromJson(
        v.asJson(),
        (v) => CustomizeCardInfo.fromJson(v.asJson()),
      ),
    );
    return rep.data;
  }

  Future<Paged<ReferralLog>> getReferralLogs({
    required int pageNum,
    required int pageSize,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.user.referralLogs,
          queryParameters: <String, dynamic>{
            'pageNum': pageNum,
            'pageSize': pageSize,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => Paged.fromJson(
        v.asJson(),
        (v) => ReferralLog.fromJson(v.asJson()),
      ),
    );
    return rep.data;
  }

  Future<String> uploadAvatar({
    required String fileContent,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .post(
          ApiPath.service.user.uploadAvatar,
          data: <String, dynamic>{
            'fileContent': fileContent,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => v as String);
    return rep.data;
  }

  Future<TokenPrices> getTokenPrices({
    required String symbol,
    required String chainName,
    required String ucid,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.price.latestPrice,
          queryParameters: <String, dynamic>{
            if (symbol.trim() case final symbol when symbol.isNotEmpty) 'symbol': symbol,
            if (chainName.trim() case final chainName when chainName.isNotEmpty) 'chainName': chainName,
            if (ucid.trim() case final ucid when ucid.isNotEmpty) 'ucid': ucid,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => TokenPrices.fromJson(v.asJson()));
    return rep.data;
  }

  Future<QRCodeDynamicResult> getUserQRCodeDynamic({CancelToken? cancelToken}) async {
    final res = await http
        .get(
          ApiPath.service.user.qrCode,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => QRCodeDynamicResult.fromJson(v.asJson()));
    return rep.data;
  }

  Future<UserInfo> getPublicProfile({
    required String code,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.profile.queryPub(cardCode: code),
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => UserInfo.fromJson(v.asJson()));
    return rep.data;
  }

  Future<List<Social>> getPublicSocials({
    required String code,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.profile.socialGetPub(cardCode: code),
          cancelToken: cancelToken,
        )
        .retry();
    final rep = ListRep.fromJson(res.data, (v) => Social.fromJson(v.asJson()));
    return rep.list;
  }

  // ETHCC相关API方法
  Future<List<String>> getEthccTopicsAll({
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.ethcc.topics,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => (v as List).map((item) => item.toString()).toList(),
    );
    return rep.data;
  }

  Future<List<String>> getEthccRolesAll({
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.ethcc.roles,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => (v as List).map((item) => item.toString()).toList(),
    );
    return rep.data;
  }

  Future<EthccProfile?> getEthccProfile({
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.ethcc.profile,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => v != null ? EthccProfile.fromJson(v.asJson()) : null,
    );
    return rep.data;
  }

  Future<EthccProfile?> getEthccPublicProfile({
    required String code,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.ethcc.queryPub(code: code),
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => v != null ? EthccProfile.fromJson(v.asJson()) : null,
    );
    return rep.data;
  }

  Future<void> updateEthccTopics({
    required String topics,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .post(
          ApiPath.service.ethcc.profileTopics,
          data: <String, dynamic>{
            'topics': topics,
          },
          cancelToken: cancelToken,
        )
        .retry();
    Rep.fromJson(res.data, (v) => v);
  }

  Future<void> updateEthccRole({
    required String role,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .post(
          ApiPath.service.ethcc.profileRoles,
          data: <String, dynamic>{
            'role': role,
          },
          cancelToken: cancelToken,
        )
        .retry();
    Rep.fromJson(res.data, (v) => v);
  }

  Future<void> updateEthccGithubHandle({
    required String githubHandle,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .post(
          ApiPath.service.ethcc.profileGithubHandle,
          data: <String, dynamic>{
            'githubHandle': githubHandle,
          },
          cancelToken: cancelToken,
        )
        .retry();
    Rep.fromJson(res.data, (v) => v);
  }

  Future<UserRelation> toggleUserFollow({
    required String referralCode,
    required bool follow,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .post(
          follow
              ? ApiPath.service.user.follow(referralCode: referralCode)
              : ApiPath.service.user.unfollow(referralCode: referralCode),
          cancelToken: cancelToken,
        )
        .retry();
    UserRelation? relation;
    final rep = Rep.fromJson(
      res.data,
      (v) => v == null ? null : UserRelation.fromJson(v.asJson()),
    );
    relation = rep.data;
    relation ??= await getUserRelation(referralCode: referralCode, cancelToken: cancelToken);
    return relation;
  }

  Future<UserRelation> getUserRelation({
    required String referralCode,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.user.relation(referralCode: referralCode),
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => UserRelation.fromJson(v.asJson()));
    return rep.data;
  }

  Future<Paged<UserFromRelation>> getFollowingList({
    required int page,
    int size = 20,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.user.followingList,
          queryParameters: <String, dynamic>{
            'pageNum': page,
            'pageSize': size,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => Paged.fromJson(
        v.asJson(),
        (v) => UserFromRelation.fromJson(v.asJson()),
      ),
    );
    return rep.data;
  }

  Future<Paged<UserFromRelation>> getFollowerList({
    required int page,
    int size = 20,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.user.followerList,
          queryParameters: <String, dynamic>{
            'pageNum': page,
            'pageSize': size,
          },
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => Paged.fromJson(
        v.asJson(),
        (v) => UserFromRelation.fromJson(v.asJson()),
      ),
    );
    return rep.data;
  }

  Future<List<EventItem>> getDiscoveryEvents({
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.discovery.configList,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = ListRep.fromJson(res.data, (v) => EventItem.fromJson(v.asJson()));
    return rep.list;
  }

  Future<List<ImageAIStyle>> getImageStyles({CancelToken? cancelToken}) async {
    final res = await http
        .get(
          ApiPath.service.image.styles,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = ListRep.fromJson(res.data, (v) => ImageAIStyle.fromJson(v.asJson()));
    return rep.list;
  }

  Future<int> getImageTaskQuota({CancelToken? cancelToken}) async {
    final res = await http
        .get(
          ApiPath.service.image.quota,
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => v as int);
    return rep.data;
  }

  Future<ImageAITaskInQueue> createImageTask({
    required String imageFilePath,
    required ImageAIStyle style,
    CancelToken? cancelToken,
    ProgressCallback? onProgress,
  }) async {
    final filename = p.basename(imageFilePath);
    final extension = p.extension(imageFilePath).removeFirst('.');
    final file = await MultipartFile.fromFile(
      imageFilePath,
      filename: filename,
      contentType: lookupMimeType(filename)?.run(DioMediaType.parse),
    );
    final imageVariant = MultipartFile.fromString(
      jsonEncode({
        'ext': extension,
        'styleId': style.id,
      }),
      contentType: DioMediaType.parse(Headers.jsonContentType),
    );
    final res = await http.post(
      ApiPath.service.image.create,
      data: FormData.fromMap({
        'file': file,
        'imageVariant': imageVariant,
      }),
      cancelToken: cancelToken,
      onSendProgress: onProgress,
    );
    final rep = Rep.fromJson(res.data, (v) => ImageAITaskInQueue.fromJson(v.asJson()));
    return rep.data;
  }

  Future<ImageAITask> getImageTaskResult({
    required String taskId,
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          ApiPath.service.image.taskResult(taskId: taskId),
          cancelToken: cancelToken,
        )
        .retry();
    final rep = Rep.fromJson(res.data, (v) => ImageAITask.fromJson(v.asJson()));
    return rep.data;
  }
}

class ApiServiceUnauthorizedInterceptor extends QueuedInterceptor {
  static const _tag = '🔒 UnauthorizedInterceptor';

  bool _unauthorizeRedirecting = false;

  @override
  void onResponse(response, handler) {
    // Skip when the request is not a service api request.
    if (!response.requestOptions.uri.toString().startsWith(envApiUrlService)) {
      handler.next(response);
      return;
    }

    try {
      if (response.data case {
        'code': final int code,
      } when code == 401) {
        if (_unauthorizeRedirecting) {
          return;
        }
        _unauthorizeRedirecting = true;
        Future<void>(() async {
          await meNavigator.removeNamedAndPushAndRemoveUntil(
            Routes.login.name,
            predicate: (_) => false,
          );
          await BoxService.clearUserBox();
        }).whenComplete(() {
          _unauthorizeRedirecting = false;
        });
      }
    } catch (e, s) {
      LogUtil.e(e, stackTrace: s, tag: _tag);
    }

    handler.next(response);
  }
}
