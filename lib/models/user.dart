import 'dart:io' show Platform;

import 'package:collection/collection.dart';
import 'package:flutter/widgets.dart' show BuildContext;
import 'package:freezed_annotation/freezed_annotation.dart';

part 'user.freezed.dart';

part 'user.g.dart';

enum ProfileMode {
  @JsonValue('DEFAULT')
  DEFAULT,
  @JsonValue('ETHCC')
  ETHCC,
  @JsonValue('')
  EMPTY,
}

@freezed
sealed class UserInfo with _$UserInfo implements UserWithAvatar {
  @Implements<UserWithAvatar>()
  const factory UserInfo({
    @JsonKey(name: 'avatar') @Default('') String avatar,
    @Json<PERSON>ey(name: 'bannerHref') @Default('') String bannerHref,
    @JsonKey(name: 'bannerImage') @Default('') String bannerImage,
    @JsonKey(name: 'cardCode') @Default('') String cardCode,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'company') @Default('') String company,
    @Json<PERSON>ey(name: 'currentType') @Default(0) int currentType,
    @JsonKey(name: 'evmWallet') @Default('') String evmWallet,
    @JsonKey(name: 'handle') @Default('') String handle,
    @JsonKey(name: 'integral') @Default(0) int integral,
    @JsonKey(name: 'lastMessageId') @Default(0) int lastMessageId,
    @JsonKey(name: 'latestMessageId') @Default(0) int latestMessageId,
    @JsonKey(name: 'name') @Default('') String name,
    @JsonKey(name: 'profileMode') @Default(ProfileMode.EMPTY) ProfileMode profileMode,
    @JsonKey(name: 'redirectUrl') @Default('') String redirectUrl,
    @JsonKey(name: 'referralCode') @Default('') String referralCode,
    @JsonKey(name: 'title') @Default('') String title,
    @JsonKey(name: 'userEmail') @Default('') String userEmail,
  }) = _UserInfo;

  factory UserInfo.fromJson(Map<String, dynamic> json) => _$UserInfoFromJson(json);
}

abstract interface class UserWithAvatar {
  String get avatar;
}

@freezed
sealed class Group with _$Group {
  const factory Group({
    @JsonKey(name: 'uniqId') required String id,
    @JsonKey(name: 'name') required String name,
    @JsonKey(name: 'description') required String description,
    @JsonKey(name: 'logo') @Default('') String logo,
    @JsonKey(name: 'userCount') @Default(1) int userCount,
    @JsonKey(name: 'creator') required String creatorName,
  }) = _Group;

  factory Group.fromJson(Map<String, dynamic> json) => _$GroupFromJson(json);
}

@freezed
sealed class UserSettingsRequest with _$UserSettingsRequest {
  const factory UserSettingsRequest({
    @JsonKey(name: 'fcmAndroid', includeIfNull: false) String? fcmAndroid,
    @JsonKey(name: 'fcmIos', includeIfNull: false) String? fcmIOS,
  }) = _UserSettingsRequest;

  factory UserSettingsRequest.fromJson(Map<String, dynamic> json) => _$UserSettingsRequestFromJson(json);

  factory UserSettingsRequest.fromPlatform({required String fcm}) {
    return UserSettingsRequest(
      fcmAndroid: Platform.isAndroid ? fcm : null,
      fcmIOS: Platform.isIOS ? fcm : null,
    );
  }
}

@freezed
sealed class UserRelation with _$UserRelation {
  const factory UserRelation({
    @JsonKey(name: 'following') @Default(false) bool following,
    @JsonKey(name: 'followedBy') @Default(false) bool followedBy,
  }) = _UserRelation;

  factory UserRelation.fromJson(Map<String, dynamic> json) => _$UserRelationFromJson(json);
}

enum UserFromRelationType {
  following,
  follower;

  String displayName(BuildContext context) {
    switch (this) {
      case UserFromRelationType.following:
        return 'Following';
      case UserFromRelationType.follower:
        return 'Followers';
    }
  }

  static UserFromRelationType? fromName(String? value) {
    if (value == null || value.isEmpty) {
      return null;
    }
    return values.firstWhereOrNull((e) => e.name == value);
  }
}

@freezed
sealed class UserFromRelation with _$UserFromRelation implements UserWithAvatar {
  @Implements<UserWithAvatar>()
  const factory UserFromRelation({
    @JsonKey(name: 'referralCode') required String referralCode,
    @JsonKey(name: 'name') @Default('') String name,
    @JsonKey(name: 'title') @Default('') String title,
    @JsonKey(name: 'company') @Default('') String company,
    @JsonKey(name: 'avatar') @Default('') String avatar,
    @JsonKey(name: 'following') @Default(false) bool following,
    @JsonKey(name: 'followedBy') @Default(false) bool followedBy,
  }) = _UserFromRelation;

  factory UserFromRelation.fromJson(Map<String, dynamic> json) => _$UserFromRelationFromJson(json);
}
