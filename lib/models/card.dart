import 'dart:convert' show jsonDecode;

import 'package:collection/collection.dart';
import 'package:flutter/material.dart' show Color;
import 'package:freezed_annotation/freezed_annotation.dart';

import '/constants/envs.dart' show envIsProd;

part 'card.freezed.dart';

part 'card.g.dart';

const groupPreviewLogoSize = 100.0;

const eventIdAllowAll = 0;

enum NfcType {
  @JsonValue('NFC215')
  NFC215,
  @JsonValue('NFC424')
  NFC424,
}

enum CardType {
  @JsonValue('STICKER')
  STICKER,
  @JsonValue('CARD')
  CARD,
  @JsonValue('WRISTBAND')
  WRISTBAND,
}

enum PrintType {
  @JsonValue('METAL')
  METAL,
  @JsonValue('NORMAL')
  NORMAL,
}

enum CardPayStatus {
  @JsonValue('NOT_PAYED')
  NOT_PAYED,
  @JsonValue('PAYED')
  PAYED,
  @JsonValue('FREE')
  FREE,
}

@freezed
sealed class Social with _$Social {
  const factory Social({
    @JsonKey(name: 'handleName') required String handleName,
    @JsonKey(name: 'id') required int id,
    @JsonKey(name: 'platformName') required String platformName,
    @JsonKey(name: 'platformUrl') required String platformUrl,
    @JsonKey(name: 'isVerify') @Default(false) bool isVerify,
  }) = _Social;

  factory Social.fromJson(Map<String, dynamic> json) => _$SocialFromJson(json);
}

/// 社交平台配置
enum SocialPlatform {
  twitter(
    name: 'Twitter',
    alias: 'X (Twitter)',
    url: 'https://x.com/',
    addon: '@',
    placeholder: 'Handle',
    demoImageKey: 'X-Twitter',
    iconImageKey: 'twitter',
  ),
  telegram(
    name: 'Telegram',
    url: 'https://t.me/',
    addon: '@',
    placeholder: 'Username',
    demoImageKey: 'Telegram',
    iconImageKey: 'telegram',
  ),
  instagram(
    name: 'Instagram',
    url: 'https://www.instagram.com/',
    placeholder: 'Username',
    demoImageKey: 'Instagram',
    iconImageKey: 'instagram',
  ),
  linkedIn(
    name: 'LinkedIn',
    url: 'https://www.linkedin.com/in/',
    placeholder: 'Profile Username',
    demoImageKey: 'LinkedIn',
    iconImageKey: 'linkedIn',
  ),
  farcaster(
    name: 'Farcaster',
    url: 'https://farcaster.xyz/',
    placeholder: 'Username',
    demoImageKey: 'Farcaster',
    iconImageKey: 'farcaster',
  ),
  memex(
    name: 'MemeX',
    url: 'https://app.memex.xyz/profile/',
    borderColor: Color(0xFFAAAAAA),
    placeholder: 'Link to your MemeX profile',
    demoImageKey: 'MemeX',
    iconImageKey: 'memex',
  ),
  whatsapp(
    name: 'Whatsapp',
    url: 'https://wa.me/',
    placeholder: 'Phone Number',
    iconImageKey: 'whatsapp',
  ),
  github(
    name: 'Github',
    url: 'https://github.com/',
    placeholder: 'Username',
    demoImageKey: 'GitHub',
    iconImageKey: 'github',
  ),
  discord(
    name: 'Discord',
    url: 'https://discord.gg/',
    placeholder: 'Server Name',
    iconImageKey: 'discord',
  ),
  tiktok(
    name: 'TikTok',
    url: 'https://www.tiktok.com/@',
    placeholder: 'Username',
    demoImageKey: 'TikTok',
    iconImageKey: 'tiktok',
  ),
  linktree(
    name: 'Linktree',
    url: 'https://linktr.ee/',
    placeholder: 'Username',
    demoImageKey: 'Linktree',
    iconImageKey: 'linkTree',
  ),
  youtube(
    name: 'Youtube',
    url: 'https://www.youtube.com/@',
    placeholder: 'Username',
    demoImageKey: 'YouTube',
    iconImageKey: 'youtube',
  ),
  calendly(
    name: 'Calendly',
    url: 'https://calendly.com/',
    placeholder: 'Username',
    iconImageKey: 'calendly',
  ),
  ordme(
    name: 'Ord.me',
    url: 'https://www.ord.me/',
    placeholder: 'Username',
    iconImageKey: 'ordme',
  ),
  zalo(
    name: 'Zalo',
    url: 'https://zalo.me/',
    placeholder: 'Number',
    iconImageKey: 'zalo',
  ),
  phone(
    name: 'Phone',
    url: 'tel:+',
    placeholder: 'Your phone number',
    iconImageKey: 'phone',
  ),
  email(
    name: 'Email',
    url: 'mailto:',
    placeholder: 'Your email',
    iconImageKey: 'email',
  ),
  link(
    name: 'Custom Link',
    url: 'https://',
    placeholder: 'Your link',
    iconImageKey: 'link',
  );

  const SocialPlatform({
    required this.name,
    this.alias,
    this.url = '',
    this.borderColor,
    this.placeholder,
    this.addon,
    this.demoImageKey,
    required this.iconImageKey,
  });

  final String name;
  final String? alias;
  final String url;
  final Color? borderColor;
  final String? placeholder;
  final String? addon;
  final String? demoImageKey;
  final String iconImageKey;

  static SocialPlatform? fromName(String? name) {
    if (name == null || name.isEmpty) {
      return null;
    }
    return SocialPlatform.values.firstWhereOrNull((e) => e.name == name);
  }

  bool matched(String value) {
    if (value.isEmpty) {
      return false;
    }
    switch (this) {
      case SocialPlatform.link:
        final regexp = RegExp(
          r'^(?!-)[A-Za-z0-9-]{1,63}(?<!-)(\.[A-Za-z0-9-]{1,63}(?<!-))*(\.[A-Za-z]{2,})$',
          caseSensitive: false,
        );
        return regexp.hasMatch(value);
      default:
        return true;
    }
  }

  List<int>? get events {
    switch (this) {
      case SocialPlatform.ordme:
        return [
          120,
          if (envIsProd) ...[122] else ...[121, 125],
        ];
      default:
        return null;
    }
  }
}

@freezed
sealed class CardInfoBasic with _$CardInfoBasic {
  const factory CardInfoBasic.activated({
    @JsonKey(name: 'activated') required bool activated,
    @JsonKey(name: 'redirectUrl') @Default('') String redirectUrl,
    @JsonKey(name: 'referralCode') required String referralCode,
  }) = CardInfoBasicActivated;

  const factory CardInfoBasic.inactivated({
    @JsonKey(name: 'id') required int id,
    @JsonKey(name: 'activated') required bool activated,
    @JsonKey(name: 'cardType') required CardType cardType,
    @JsonKey(name: 'backCover') @Default('') String backCover,
    @JsonKey(name: 'redirectUrl') @Default('') String redirectUrl,
  }) = CardInfoBasicInactivated;

  /// Use [CardInfoBasicConverter] to get rid of union type requirements.
  factory CardInfoBasic.fromJson(Map<String, dynamic> json) => CardInfoBasicConverter.fromJson(json);
}

abstract final class CardInfoBasicConverter {
  static CardInfoBasic fromJson(Map<String, dynamic> json) {
    if (json['activated'] == true) {
      return CardInfoBasicActivated.fromJson(json);
    } else {
      return CardInfoBasicInactivated.fromJson(json);
    }
  }
}

@freezed
sealed class CardInfo with _$CardInfo {
  const factory CardInfo({
    @JsonKey(name: 'active') required bool active,
    @JsonKey(name: 'activeTime') @Default('') String activeTime,
    @JsonKey(name: 'backCover') @Default('') String backCover,
    @JsonKey(name: 'cardCode') @Default('') String cardCode,
    @JsonKey(name: 'cardType') @Default(CardType.CARD) CardType cardType,
    @JsonKey(name: 'chainId') @Default(0) int chainId,
    @JsonKey(name: 'card3EventId') @Default(0) int eventId,
    @JsonKey(name: 'eventName') @Default('') String eventName,
    @JsonKey(name: 'id') @Default(0) int id,
    @JsonKey(name: 'isActive') @Default(false) bool isActive,
    @JsonKey(name: 'referralCode') @Default('') String referralCode,
    @JsonKey(name: 'virtualCard') @Default(false) bool virtualCard,
    @JsonKey(name: 'nfcType') @Default(NfcType.NFC215) NfcType nfcType,
    @JsonKey(name: 'redirectUrl') @Default('') redirectUrl,
  }) = _CardInfo;

  factory CardInfo.fromJson(Map<String, dynamic> json) => _$CardInfoFromJson(json);
}

@freezed
sealed class CoverInfo with _$CoverInfo {
  const factory CoverInfo({
    @JsonKey(name: 'activeMode') @Default('') String activeMode,
    @JsonKey(name: 'backCover') @Default('') String backCover,
    @JsonKey(name: 'eventId') @Default('') String eventId,
    @JsonKey(name: 'eventName') @Default('') String eventName,
    @JsonKey(name: 'paymentLink') @Default('') String paymentLink,
    @JsonKey(name: 'price') @Default(0) int price,
    @JsonKey(name: 'priceDescription') @Default('') String priceDescription,
    @JsonKey(name: 'printType') @Default(PrintType.NORMAL) PrintType printType,
    @JsonKey(name: 'thirdPartyLink') @Default('') String thirdPartyLink,
  }) = _CoverInfo;

  factory CoverInfo.fromJson(Map<String, dynamic> json) => _$CoverInfoFromJson(json);
}

@freezed
sealed class CreateCardCoverResponse with _$CreateCardCoverResponse {
  const factory CreateCardCoverResponse({
    @JsonKey(name: 'code') required String code,
    @JsonKey(name: 'paymentLink') @Default('') String paymentLink,
  }) = _CreateCardCoverResponse;

  factory CreateCardCoverResponse.fromJson(Map<String, dynamic> json) => _$CreateCardCoverResponseFromJson(json);
}

@freezed
sealed class CustomizeCardInfo with _$CustomizeCardInfo {
  const factory CustomizeCardInfo({
    @JsonKey(name: 'username') required String name,
    @JsonKey(name: 'title') @Default('') String title,
    @JsonKey(name: 'company') @Default('') String company,
    @JsonKey(name: 'image') @Default('') String image,
    @JsonKey(name: 'code') required String code,
    @JsonKey(name: 'preview') @Default('') String preview,
    @JsonKey(name: 'backCover') @Default('') String backCover,
    @JsonKey(name: 'eventName') @Default('') String eventName,
    @JsonKey(name: 'isPrinted') @Default(false) bool printed,
    @JsonKey(name: 'printType') required PrintType printType,
    @JsonKey(name: 'trackingNumber') @Default('') String trackingNumber,
    @JsonKey(name: 'logisticsCompany') @Default('') String trackingCompany,
    @JsonKey(name: 'price') @Default(0) int payPrice,
    @JsonKey(name: 'payStatus') @Default(CardPayStatus.FREE) CardPayStatus payStatus,
    @JsonKey(name: 'thirdPartyLink', readValue: CustomizeCardPayInfo.readValue) required CustomizeCardPayInfo payInfo,
  }) = _CustomizeCardInfo;

  factory CustomizeCardInfo.fromJson(Map<String, dynamic> json) => _$CustomizeCardInfoFromJson(json);
}

@freezed
sealed class CustomizeCardPayInfo with _$CustomizeCardPayInfo {
  const factory CustomizeCardPayInfo({
    @JsonKey(name: 'link') @Default('') String link,
    @JsonKey(name: 'priceUnit') @CustomizeCardPriceUnitConverter() CustomizeCardPriceUnit? priceUnit,
    @JsonKey(name: 'shippingUnit') @CustomizeCardPriceUnitConverter() CustomizeCardPriceUnit? shippingUnit,
  }) = _CustomizeCardPayInfo;

  factory CustomizeCardPayInfo.fromJson(Map<String, dynamic> json) => _$CustomizeCardPayInfoFromJson(json);

  static Map<String, dynamic> readValue(Map json, String key) {
    return jsonDecode(json[key]);
  }
}

@freezed
sealed class CustomizeCardPriceUnit with _$CustomizeCardPriceUnit {
  const factory CustomizeCardPriceUnit({
    @JsonKey(name: 'symbol') @Default('') String symbol,
    @JsonKey(name: 'amount') @Default('') String amount,
    @JsonKey(name: 'currency') @Default('') String currency,
  }) = _CustomizeCardPriceUnit;

  const CustomizeCardPriceUnit._();

  factory CustomizeCardPriceUnit.fromJson(Map<String, dynamic> json) => _$CustomizeCardPriceUnitFromJson(json);

  String get display => '$symbol $amount $currency';
}

final class CustomizeCardPriceUnitConverter implements JsonConverter<CustomizeCardPriceUnit?, String?> {
  const CustomizeCardPriceUnitConverter();

  @override
  CustomizeCardPriceUnit? fromJson(String? json) {
    if (json?.trim().split('/') case [
      final symbol,
      final amount,
      final currency,
    ]) {
      return CustomizeCardPriceUnit(symbol: symbol, amount: amount, currency: currency);
    }
    return null;
  }

  @override
  String? toJson(CustomizeCardPriceUnit? object) {
    if (object == null) {
      return null;
    }
    return '${object.symbol}/${object.amount}/${object.currency}';
  }
}

@freezed
sealed class EthccProfile with _$EthccProfile {
  const factory EthccProfile({
    @JsonKey(name: 'githubHandle') @Default('') String githubHandle,
    @JsonKey(
      name: 'topics',
      fromJson: EthccProfile.fromJoinedString,
      toJson: EthccProfile.toJoinedString,
    )
    @Default([])
    List<String> topics,
    @JsonKey(
      name: 'role',
      fromJson: EthccProfile.fromJoinedString,
      toJson: EthccProfile.toJoinedString,
    )
    @JsonKey(name: 'role')
    @Default([])
    List<String> roles,
  }) = _EthccProfile;

  const EthccProfile._();

  factory EthccProfile.fromJson(Map<String, dynamic> json) => _$EthccProfileFromJson(json);

  static List<String> fromJoinedString(String value) {
    value = value.trim();
    if (value.isEmpty) {
      return [];
    }
    return value.split(',').map((e) => e.trim()).where((e) => e.isNotEmpty).toList();
  }

  static String toJoinedString(List<String> value) => value.map((e) => e.trim()).where((e) => e.isNotEmpty).join(',');

  bool get isEmpty => githubHandle.isEmpty && topics.isEmpty && roles.isEmpty;
}

@freezed
sealed class UpdateEthccTopicsRequest with _$UpdateEthccTopicsRequest {
  const factory UpdateEthccTopicsRequest({
    @JsonKey(name: 'topics') required String topics,
  }) = _UpdateEthccTopicsRequest;

  factory UpdateEthccTopicsRequest.fromJson(Map<String, dynamic> json) => _$UpdateEthccTopicsRequestFromJson(json);
}

@freezed
sealed class UpdateEthccGithubRequest with _$UpdateEthccGithubRequest {
  const factory UpdateEthccGithubRequest({
    @JsonKey(name: 'githubHandle') required String githubHandle,
  }) = _UpdateEthccGithubRequest;

  factory UpdateEthccGithubRequest.fromJson(Map<String, dynamic> json) => _$UpdateEthccGithubRequestFromJson(json);
}

@freezed
sealed class QRCodeDynamicResult with _$QRCodeDynamicResult {
  const factory QRCodeDynamicResult({
    @JsonKey(name: 'url') required String url,
    @JsonKey(
      name: 'expireTime',
      fromJson: QRCodeDynamicResult._expireFromJson,
      toJson: QRCodeDynamicResult._expireToJson,
    )
    required DateTime expireTime,
    @JsonKey(name: 'qrcodeValidity') required int validSeconds,
  }) = _QRCodeDynamicResult;

  factory QRCodeDynamicResult.fromJson(Map<String, dynamic> json) => _$QRCodeDynamicResultFromJson(json);

  static DateTime _expireFromJson(int value) {
    return DateTime.fromMillisecondsSinceEpoch(value * 1000);
  }

  static int _expireToJson(DateTime value) {
    return value.millisecondsSinceEpoch ~/ 1000;
  }
}
