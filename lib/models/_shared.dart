import 'package:decimal/decimal.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

int hexToInt(String value) => int.parse(value, radix: 16);

bool intToBool(int value) => value == 1;

bool intStringToBool(String value) => value == '1';

String intToHex(int value) => value.toRadixString(16);

int boolToInt(bool value) => value ? 1 : 0;

String boolToIntString(bool value) => value ? '1' : '0';

final class Iso8601DateTimeConverter implements JsonConverter<DateTime, String> {
  const Iso8601DateTimeConverter();

  @override
  DateTime fromJson(String json) {
    return DateTime.parse(json);
  }

  @override
  String toJson(DateTime object) {
    return object.toIso8601String();
  }
}

final class EpochDateTimeConverter implements JsonConverter<DateTime, int> {
  const EpochDateTimeConverter();

  @override
  DateTime fromJson(int json) {
    return DateTime.fromMillisecondsSinceEpoch(json, isUtc: true);
  }

  @override
  int toJson(DateTime object) {
    return object.millisecondsSinceEpoch;
  }
}

final class EpochDateTimeNullableConverter implements JsonConverter<DateTime?, int?> {
  const EpochDateTimeNullableConverter();

  @override
  DateTime? fromJson(int? json) {
    return json == null ? null : DateTime.fromMillisecondsSinceEpoch(json, isUtc: true);
  }

  @override
  int? toJson(DateTime? object) {
    return object?.millisecondsSinceEpoch;
  }
}

final class JsonUserIdConverter implements JsonConverter<int, String> {
  const JsonUserIdConverter();

  @override
  int fromJson(String json) => hexToInt(json);

  @override
  String toJson(int object) => intToHex(object);
}

final class JsonUserIdsConverter implements JsonConverter<List<int>, List> {
  const JsonUserIdsConverter();

  @override
  List<int> fromJson(List json) {
    return json.cast<String>().map(hexToInt).toList();
  }

  @override
  List<String> toJson(List<int> object) {
    return object.map(intToHex).toList();
  }
}

final class NumberIntRequiredConverter implements JsonConverter<int, Object?> {
  const NumberIntRequiredConverter();

  @override
  int fromJson(Object? json) {
    if (json is int) {
      return json;
    }
    return int.parse('$json');
  }

  @override
  String toJson(int object) {
    return object.toString();
  }
}

final class NumberDecimalConverter implements JsonConverter<Decimal?, Object?> {
  const NumberDecimalConverter();

  @override
  Decimal? fromJson(Object? json) {
    if (json is int) {
      return Decimal.fromInt(json);
    } else if (json is double) {
      return Decimal.parse(json.toString());
    } else if (json is String) {
      return Decimal.parse(json);
    }
    return null;
  }

  @override
  String? toJson(Decimal? object) {
    return object?.toString();
  }
}

final class NumberDecimalRequiredConverter implements JsonConverter<Decimal, Object> {
  const NumberDecimalRequiredConverter();

  @override
  Decimal fromJson(Object json) {
    if (json is int) {
      return Decimal.fromInt(json);
    } else if (json is double) {
      return Decimal.parse(json.toString());
    } else if (json is String) {
      return Decimal.parse(json);
    }
    throw StateError('$json cannot be parsed as decimal.');
  }

  @override
  String toJson(Decimal object) {
    return object.toString();
  }
}
