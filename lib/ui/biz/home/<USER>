import 'dart:ui' as ui;

import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:sliver_tools/sliver_tools.dart';

import '/models/business.dart' show EventItem, EventItemDisplayEnv;
import '/models/card.dart' show CardInfo;
import '/provider/api.dart' show apiServiceProvider;
import '/provider/card.dart';
import '/provider/user.dart';

class _EventCategory {
  const _EventCategory({
    required this.title,
    required this.items,
  });

  final String title;
  final List<EventItem> items;
}

class Fun extends StatelessWidget {
  const Fun({super.key});

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: themeBy(
        meTheme: defaultMEThemeDark,
        locale: Localizations.localeOf(context),
      ),
      child: const BrightnessLayer(
        brightness: Brightness.dark,
        child: _MainBody(),
      ),
    );
  }
}

final _listProvider = FutureProvider.autoDispose<List<_EventCategory>>((ref) async {
  final ct = ref.cancelToken();
  ref.onDispose(() {
    ct.cancel();
  });

  late final List<EventItem> events;
  late final List<CardInfo> cards;

  await Future.wait([
    ref
        .read(apiServiceProvider)
        .getDiscoveryEvents(cancelToken: ct)
        .then((r) => events = r.sorted((a, b) => a.sort.compareTo(b.sort))),
    ref.read(fetchMyCardsProvider.future).then((r) => cards = r),
  ]);

  final cardEventIds = cards.map((e) => e.eventId);
  final grouped = <String, List<EventItem>>{};
  for (final event in events) {
    void add() {
      if (event.eventIds.isEmpty || event.eventIds.any(cardEventIds.contains)) {
        final category = event.category.or('Other');
        grouped.putIfAbsent(category, () => <EventItem>[]).add(event);
      }
    }

    if (!isSealed) {
      add();
      continue;
    }

    if (event.displayEnvs case final envs when envs.isNotEmpty) {
      if (isAuditing) {
        if (envs.contains(EventItemDisplayEnv.auditing)) {
          add();
        }
        continue;
      }

      if (envs.contains(EventItemDisplayEnv.production)) {
        add();
        continue;
      }
    }
  }

  final categories = grouped.entries.map<_EventCategory>((entry) {
    return _EventCategory(title: entry.key, items: entry.value);
  }).toList();

  return categories;
});

class _MainBody extends ConsumerWidget {
  const _MainBody();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final result = ref.watch(_listProvider);
    return Scaffold(
      body: RefreshIndicator(
        displacement: MediaQuery.paddingOf(context).top,
        onRefresh: () async {
          ref.invalidate(fetchUserInfoProvider);
          ref.invalidate(fetchMyCardsProvider);
          ref.invalidate(_listProvider);
          await Future.wait([
            ref.read(fetchUserInfoProvider().future),
            ref.read(fetchMyCardsProvider.future),
            ref.read(_listProvider.future),
          ]);
        },
        child: CustomScrollView(
          slivers: [
            const SliverToBoxAdapter(child: _PointsCard()),
            if (result.valueOrNull case final data?)
              ...data.map(
                (category) => ProviderScope(
                  overrides: [_categoryItemProvider.overrideWithValue(category)],
                  child: const _CategoryItem(),
                ),
              )
            else if (result.isLoading)
              const SliverToBoxAdapter(child: AppLoading())
            else
              SliverEmptyView(
                message: switch (result.error) {
                  final e when isNetworkError(e) => context.l10nME.networkError,
                  _ => context.l10nME.clickToRetryButton,
                },
              ),
            const SliverGap.v(24.0),
          ],
        ),
      ),
    );
  }
}

class _PointsCard extends ConsumerWidget {
  const _PointsCard();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final localUser = ref.watch(userRepoProvider);
    final userInfoResult = ref.watch(fetchUserInfoProvider());
    final userInfo = userInfoResult.valueOrNull ?? localUser;
    return Tapper(
      onTap: () => Navigator.of(context).pushNamed(Routes.funPointRecord.name),
      child: AppBlurBackground(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Gap.topPadding(context),
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16.0).copyWith(top: 4.0),
              padding: const EdgeInsets.all(5.0),
              alignment: AlignmentDirectional.centerEnd,
              child: const Icon(Icons.history, size: 30, color: Colors.white),
            ),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0).copyWith(top: 0.0),
              child: Column(
                spacing: 4.0,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    'Points',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: 24,
                    ),
                  ),
                  Text(
                    userInfo?.integral.toString() ?? '---',
                    style: const TextStyle(
                      fontSize: 48,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            const Gap.v(40.0),
          ],
        ),
      ),
    );
  }
}

final _categoryItemProvider = Provider.autoDispose<_EventCategory>(
  (ref) => throw UnimplementedError(),
);

class _CategoryItem extends ConsumerWidget {
  const _CategoryItem();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final category = ref.watch(_categoryItemProvider);
    return MultiSliver(
      pushPinnedChildren: true,
      children: [
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 24, 16, 0),
            child: Text(
              category.title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
        ),
        const SliverGap.v(8.0),
        SliverPadding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          sliver: SliverGrid(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 10.0,
              mainAxisSpacing: 10.0,
              childAspectRatio: 89 / 65,
            ),
            delegate: SliverChildBuilderDelegate(
              (context, index) => ProviderScope(
                overrides: [_eventItemProvider.overrideWithValue(category.items[index])],
                child: const _EventItem(),
              ),
              childCount: category.items.length,
            ),
          ),
        ),
      ],
    );
  }
}

final _eventItemProvider = Provider.autoDispose<EventItem>(
  (ref) => throw UnimplementedError(),
);

class _EventItem extends ConsumerWidget {
  const _EventItem();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final event = ref.watch(_eventItemProvider);
    return RippleTap(
      onTap: () {
        if (event.isNativeLink) {
          if (routeNames.contains(event.link)) {
            meNavigator.pushNamed(event.link);
          } else {
            Card3ToastUtil.showToast(message: 'No matched event: ${event.link}');
          }
        } else {
          final String url;
          if (Uri.tryParse(event.link) case final u?) {
            if (u.scheme.isEmpty || u.host.isEmpty) {
              url = '$envUrlCard3${event.link}';
            } else {
              url = event.link;
            }
          } else {
            url = '$envUrlCard3${event.link}';
          }
          meNavigator.pushNamed(
            Routes.webview.name,
            arguments: Routes.webview.d(url: url, title: event.title),
          );
        }
      },
      color: context.meTheme.primaryTextColor.withValues(alpha: 0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.0),
        side: BorderSide(color: context.theme.dividerColor),
      ),
      child: Stack(
        fit: StackFit.expand,
        children: [
          ClipRRect(
            child: ImageFiltered(
              imageFilter: ui.ImageFilter.blur(sigmaX: 40.0, sigmaY: 40.0),
              child: FractionallySizedBox(
                widthFactor: 0.5,
                heightFactor: 0.5,
                alignment: AlignmentDirectional.centerEnd,
                child: MEImage(event.image, fit: BoxFit.contain),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              spacing: 8.0,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  spacing: 2.0,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AutoSizeText(
                      event.title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      event.desc,
                      style: const TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
                Expanded(
                  child: Align(
                    alignment: AlignmentDirectional.bottomEnd,
                    child: MEImage(event.image, fit: BoxFit.contain),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
