import 'package:card3/exports.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '/provider/card.dart' show validateETHCCProfileProvider;
import '/provider/settings.dart' show selectedIndexProvider;

class Settings extends ConsumerWidget {
  const Settings({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isETHCCMode = ref.watch(validateETHCCProfileProvider(validateProfile: false)).valueOrNull == true;

    final theme = context.theme;
    return CustomScrollView(
      slivers: [
        CupertinoSliverNavigationBar(
          largeTitle: const Text('Settings'),
          padding: const EdgeInsetsDirectional.symmetric(horizontal: 8.0),
          backgroundColor: theme.platformDarwin ? Colors.transparent : theme.scaffoldBackgroundColor,
          brightness: theme.brightness,
          automaticallyImplyLeading: false,
          trailing: CupertinoButton(
            onPressed: () async {
              final result = await TinyDialog.show(
                text: 'Are you sure you want to logout?',
                buttons: (context) => [
                  Row(
                    spacing: 10.0,
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      TextButton(
                        onPressed: () => Navigator.of(context).maybePop(false),
                        child: Text(context.l10nME.cancelButton),
                      ),
                      ElevatedButton(
                        onPressed: () => Navigator.of(context).maybePop(true),
                        child: Text(context.l10nME.confirmButton),
                      ),
                    ],
                  ),
                ],
              );
              if (result != true) {
                return;
              }
              ChainManager.instance.dispose();
              privyClient.logout();
              ref.read(userRepoProvider.notifier).reset();
              ref.read(selectedIndexProvider.notifier).state = 0;
              meNavigator.removeNamedAndPushAndRemoveUntil(
                Routes.login.name,
                predicate: (_) => false,
              );
            },
            sizeStyle: CupertinoButtonSize.small,
            child: const Text('Logout'),
          ),
        ),
        //   Consumer(
        //     builder: (context, ref, _) {
        //       final themeMode = ref.watch(settingsProvider.select((p) => p.themeMode));
        //       return IconButton(
        //         icon: Icon(
        //           themeMode == ThemeMode.light ? Icons.dark_mode : Icons.light_mode,
        //         ),
        //         onPressed: () {
        //           final target = themeMode != ThemeMode.dark ? ThemeMode.dark : ThemeMode.light;
        //           ref.read(settingsProvider).themeMode = target;
        //         },
        //       );
        //     },
        //   ),
        SliverPadding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
          sliver: SliverList.list(
            children: [
              _buildSettingItem(
                context,
                icon: const Icon(
                  Icons.account_balance_wallet,
                  color: Colors.white,
                  size: 26,
                ),
                title: 'Wallets',
                onTap: () => AppLoading.run(
                  until: 1.seconds,
                  () async {
                    final chainManager = ChainManager.instance;
                    if (chainManager.initializing) {
                      await chainManager.initialize();
                    }
                    final state = await privyClient.getAuthState();
                    final user = state.user;
                    if (chainManager.initialized) {
                      meNavigator.pushNamed(Routes.walletPortfolio.name);
                    } else if (user != null) {
                      meNavigator.pushNamed(Routes.walletAuthenticate.name);
                    } else {
                      meNavigator.pushNamed(Routes.walletManagement.name);
                    }
                  },
                ),
              ),
              _buildSettingItem(
                context,
                icon: Center(
                  child: Assets.icons.setting.personShield.svg(
                    width: 24,
                    height: 24,
                    colorFilter: Colors.white.filter,
                  ),
                ),
                title: 'Account Security',
                onTap: () {
                  // 导航到钱包管理页面
                  meNavigator.pushNamed(Routes.settingAccount.name);
                },
              ),
              _buildSettingItem(
                context,
                icon: Center(
                  child: Assets.icons.setting.printCards.svg(
                    width: 24.0,
                    height: 24.0,
                    colorFilter: Colors.white.filter,
                  ),
                ),
                title: 'Print Cards',
                onTap: () {
                  meNavigator.pushNamed(Routes.customizePrints.name);
                },
              ),
              if (isETHCCMode) ...[
                _buildSettingItem(
                  context,
                  icon: Center(
                    child: Assets.icons.setting.mode.svg(
                      width: 24,
                      height: 24,
                      colorFilter: Colors.white.filter,
                    ),
                  ),
                  title: 'Mode',
                  onTap: () {
                    meNavigator.pushNamed(Routes.settingMode.name);
                  },
                ),
              ],
              if (!isSealed) ...[
                _buildSettingItem(
                  context,
                  icon: const Icon(
                    Icons.text_snippet,
                    color: Colors.white,
                    size: 24,
                  ),
                  title: 'WebView test',
                  onTap: () async {
                    String url = '';
                    final result = await TinyDialog.show(
                      text: 'Enter URL',
                      buttons: (context) => [
                        TextField(
                          autofocus: true,
                          autofillHints: [AutofillHints.url],
                          onChanged: (value) => url = value,
                        ),
                        Row(
                          spacing: 10.0,
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            TextButton(
                              onPressed: () => Navigator.of(context).maybePop(false),
                              child: Text(context.l10nME.cancelButton),
                            ),
                            ElevatedButton(
                              onPressed: () => Navigator.of(context).maybePop(true),
                              child: Text(context.l10nME.confirmButton),
                            ),
                          ],
                        ),
                      ],
                    );
                    if (result != true || url.isEmpty) {
                      return;
                    }
                    // 导航到通知设置页面
                    meNavigator.pushNamed(
                      Routes.webview.name,
                      arguments: Routes.webview.d(url: url, title: 'WebView'),
                    );
                  },
                ),
              ],
              _buildSettingItem(
                context,
                icon: const Icon(
                  Icons.help_outline,
                  color: Colors.white,
                  size: 24,
                ),
                iconWidget: Assets.icons.setting.about.svg(
                  width: 40,
                  height: 40,
                  colorFilter: Colors.grey.filter,
                ),
                title: 'About Card3',
                onTap: () {
                  // 导航到关于页面
                  meNavigator.pushNamed(Routes.settingAbout.name);
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  // 设置选项项目
  Widget _buildSettingItem(
    BuildContext context, {
    required Widget icon,
    required String title,
    required VoidCallback onTap,
    Widget? iconWidget,
  }) {
    return RippleTap(
      onTap: onTap,
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      padding: const EdgeInsets.all(16.0),
      borderRadius: BorderRadius.circular(16),
      color: context.theme.cardColor,
      child: Row(
        children: [
          if (iconWidget != null) iconWidget,
          if (iconWidget == null)
            // 图标背景
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: context.themeColor,
                shape: BoxShape.circle,
              ),
              child: icon,
            ),
          const SizedBox(width: 16),
          // 标题
          Expanded(
            child: Text(
              title,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
          ),
          // 箭头
          const Icon(Icons.arrow_forward_ios, size: 20),
        ],
      ),
    );
  }
}
