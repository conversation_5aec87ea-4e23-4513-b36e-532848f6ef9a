import 'dart:async';

import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/feat/nfc/helper.dart';
import '/models/card.dart';
import '/provider/api.dart';
import '/provider/card.dart';
import '/provider/user.dart';
import '/ui/widgets/social/data.dart' show SocialSvgIcon;
import '/ui/widgets/social/social_grid.dart';
import '/ui/widgets/topic.dart';

class Home extends ConsumerStatefulWidget {
  const Home({super.key});

  @override
  ConsumerState<Home> createState() => _HomeState();
}

class _HomeState extends ConsumerState<Home> {
  bool _hasShownActivationGuide = false;

  // 检查是否需要显示激活引导
  void _checkActivationGuide() {
    if (_hasShownActivationGuide || !mounted) {
      return;
    }

    final cardsResult = ref.watch(fetchMyCardsProvider);
    final cards = cardsResult.valueOrNull;
    final shouldShowGuide = ref.watch(watchIsActiveGuideProvider);

    // 只有当cards数据加载完成且需要显示引导时才显示
    if (shouldShowGuide == true && cards != null && !_hasShownActivationGuide) {
      _hasShownActivationGuide = true;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _showActivationGuideSheet();
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // 每次build时检查是否需要显示激活引导
    _checkActivationGuide();

    return RefreshIndicator(
      onRefresh: () => _refreshData(ref),
      displacement: MediaQuery.paddingOf(context).top + 48.0,
      child: const Column(
        children: [
          _Header(),
          Expanded(child: _MainBody()),
        ],
      ),
    );
  }

  // 显示激活引导弹窗
  void _showActivationGuideSheet() {
    if (!mounted) {
      return;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题和关闭按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Got a Card3 NFC item?',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  color: Colors.black,
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            const SizedBox(height: 60),

            // 卡片图像
            SizedBox(
              width: 200,
              height: 212,
              child: Assets.icons.aguide.svg(
                width: 200,
                height: 212,
              ),
            ),

            const SizedBox(height: 60),
            // 激活按钮
            Column(
              children: [
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context); // 关闭底部弹窗

                    // 触发主NFC扫描器
                    Future.delayed(const Duration(milliseconds: 300), () {
                      NfcHelper.startPollingManually();
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ColorName.themeColorDark,
                    minimumSize: const Size(double.infinity, 56),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                  ),
                  child: const Text(
                    'Activate Now',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(height: 20),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

Future<void> _refreshData(WidgetRef ref) async {
  ref.invalidate(fetchUserInfoProvider());
  ref.invalidate(fetchMyCardsProvider);
  ref.invalidate(fetchEthccProfileProvider());
  ref.invalidate(fetchSocialsProvider());
  await Future.wait([
    ref.read(fetchUserInfoProvider().future),
    ref.read(fetchMyCardsProvider.future),
    ref.read(fetchEthccProfileProvider().future),
    ref.read(fetchSocialsProvider().future),
  ]);
}

class _Header extends ConsumerWidget {
  const _Header();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final localUser = ref.watch(userRepoProvider);
    final userInfoResult = ref.watch(fetchUserInfoProvider());
    final userInfo = userInfoResult.valueOrNull ?? localUser;
    final cardsResult = ref.watch(fetchMyCardsProvider);
    final cards = cardsResult.valueOrNull;
    final filteredList = cards?.where((card) => !card.virtualCard).toList();
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0).copyWith(
        top: MediaQuery.paddingOf(context).top + 8.0,
        bottom: 8.0,
      ),
      child: Row(
        children: [
          Stack(
            clipBehavior: Clip.none,
            children: [
              Tapper(
                onTap: () {
                  meNavigator.pushNamed(Routes.notification.name);
                },
                child: Assets.icons.message.svg(
                  width: 36,
                  height: 36,
                  colorFilter: context.meTheme.primaryTextColor.filter,
                ),
              ),
              if (userInfo case final user? when user.lastMessageId != user.latestMessageId)
                Positioned(
                  right: -2,
                  top: -2,
                  child: Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 2),
                    ),
                  ),
                ),
            ],
          ),
          Expanded(
            child: Center(
              child: Tapper(
                onTap: () {
                  // 显示卡片选择底部弹窗
                  showModalBottomSheet(
                    context: context,
                    backgroundColor: ColorName.cardColorDark,
                    clipBehavior: Clip.antiAlias,
                    scrollControlDisabledMaxHeightRatio: 0.8,
                    builder: (context) => _CardSheet(cards: filteredList ?? []),
                  );
                },
                child: Container(
                  height: 44,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(50),
                    border: Border.all(color: Colors.grey),
                  ),
                  child: Row(
                    spacing: 4.0,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        margin: const EdgeInsetsDirectional.only(end: 4.0),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: context.theme.iconTheme.color,
                        ),
                        child: const Text(
                          'CARDS',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      Text(
                        '× ${filteredList?.length ?? 0}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Icon(
                        Icons.keyboard_arrow_down,
                        color: context.textTheme.bodyMedium?.color,
                        size: 20,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          Tapper(
            onTap: () {
              NfcHelper.startPollingManually();
            },
            child: Assets.icons.homeCardNfc.svg(
              width: 36,
              height: 36,
              colorFilter: context.meTheme.primaryTextColor.filter,
            ),
          ),
        ],
      ),
    );
  }
}

class _MainBody extends ConsumerWidget {
  const _MainBody();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final localUser = ref.watch(userRepoProvider);
    final userInfoResult = ref.watch(fetchUserInfoProvider());
    final userInfo = userInfoResult.valueOrNull ?? localUser;

    final isETHCCMode = ref.watch(validateETHCCProfileProvider(validateProfile: true)).valueOrNull == true;

    return Stack(
      fit: StackFit.expand,
      children: [
        ListView(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          children: [
            // 个人资料卡片
            const _ProfileCard(),
            if (isETHCCMode == true) const TopicWidget() else const SizedBox(height: 8),
            // 社交链接部分
            const _Socials(),
            ThemeTextButton.textOnly(
              onPressed: () {
                final user = userInfo;
                if (user == null) {
                  return;
                }
                final code = user.referralCode;
                context.navigator.pushNamed(
                  Routes.socialProfile.name,
                  arguments: Routes.socialProfile.d(
                    code: code,
                    profile: user == userInfo ? user : null,
                  ),
                );
              },
              margin: const EdgeInsets.only(top: 16.0),
              textStyle: TextStyle(color: context.themeColor),
              text: 'Preview',
            ),
            const Gap.v(72.0),
          ],
        ),
        Positioned.fill(
          top: null,
          child: Center(
            child: RippleTap(
              onTap: () => Navigator.of(context).pushNamed(Routes.share.name),
              width: 240.0,
              height: 48.0,
              elevation: 2.0,
              alignment: Alignment.center,
              margin: const EdgeInsets.all(16.0),
              borderRadius: RadiusConstants.max,
              color: context.themeColor,
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Share',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18.0,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Icon(
                    Icons.arrow_outward,
                    color: Colors.white,
                    size: 24.0,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class _ProfileCard extends ConsumerWidget {
  const _ProfileCard();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final localUser = ref.watch(userRepoProvider);
    final userInfoResult = ref.watch(fetchUserInfoProvider());
    final userInfo = userInfoResult.valueOrNull ?? localUser;

    return GestureDetector(
      onTap: () {
        Navigator.of(context).pushNamed(Routes.socialEditProfile.name);
      },
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // 卡片主体
          Container(
            margin: const EdgeInsets.only(top: 40),
            padding: const EdgeInsets.only(top: 50, bottom: 16),
            width: double.infinity,
            decoration: BoxDecoration(
              color: context.theme.cardColor,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  spreadRadius: 1,
                  blurRadius: 5,
                ),
              ],
            ),
            child: Column(
              spacing: 4.0,
              children: [
                // 姓名
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text(
                    switch (userInfo?.name) {
                      final n? when n.isNotEmpty => n,
                      _ => 'Name',
                    },
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: switch (userInfo?.name) {
                        final n? when n.isNotEmpty => null,
                        _ => Colors.grey,
                      },
                    ),
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                // 职位
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text(
                    userInfo?.title.or('Title') ?? 'Title',
                    style: TextStyle(
                      fontSize: 16,
                      color: userInfo?.title.isEmpty == true ? context.textTheme.bodySmall?.color : null,
                    ),
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
                // 公司
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text(
                    userInfo?.company.or('Company') ?? 'Company',
                    style: TextStyle(
                      fontSize: 16,
                      color: userInfo?.company.isEmpty == true ? context.textTheme.bodySmall?.color : null,
                    ),
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ],
            ),
          ),
          // 头像（绝对定位在卡片顶部）
          Positioned.fill(
            bottom: null,
            child: Center(
              child: DecoratedBox(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withValues(alpha: 0.3),
                      spreadRadius: 1,
                      blurRadius: 3,
                    ),
                  ],
                ),
                child: UserAvatar(user: userInfo, dimension: 80.0),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

final _socialsReorderingListProvider = StateProvider.autoDispose<List<Social>?>((ref) => null);

class _Socials extends ConsumerStatefulWidget {
  const _Socials();

  @override
  ConsumerState<_Socials> createState() => _SocialsState();
}

class _SocialsState extends ConsumerState<_Socials> {
  CancelToken? _cancelToken;

  @override
  Widget build(BuildContext context) {
    final socialsResult = ref.watch(fetchSocialsProvider());
    final socialsReordering = ref.watch(_socialsReorderingListProvider);
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            spacing: 8.0,
            children: [
              const Expanded(
                child: Text(
                  'Social Links',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Tapper(
                onTap: () {
                  if (socialsResult.hasError && !socialsResult.isLoading) {
                    ref.invalidate(fetchSocialsProvider());
                    return;
                  }
                  showModalBottomSheet(
                    context: context,
                    scrollControlDisabledMaxHeightRatio: 0.7,
                    builder: (context) => const _SocialDialog(),
                  );
                },
                child: Container(
                  width: 36,
                  height: 36,
                  padding: const EdgeInsets.all(6.0),
                  decoration: BoxDecoration(
                    color: ColorName.primaryTextColorLight,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    child: switch (socialsResult.isLoading) {
                      true => const AppLoading(),
                      false => Icon(
                        socialsResult.hasError ? Icons.refresh_rounded : Icons.add,
                        color: Colors.white,
                      ),
                    },
                  ),
                ),
              ),
            ],
          ),

          // 根据社交数据的加载状态显示不同的内容
          socialsResult.when(
            loading: () => const Padding(
              padding: EdgeInsets.symmetric(vertical: 24.0),
              child: Center(child: CircularProgressIndicator()),
            ),
            error: (error, stack) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 16.0),
              child: Text(
                'load social links failed: '
                '${isNetworkError(error) ? context.l10nME.networkError : error}',
                style: const TextStyle(color: Colors.red),
              ),
            ),
            data: (data) {
              if (data.isEmpty) {
                return _buildSocialsPlaceholder(context);
              }
              final List<Social> list;
              if (socialsReordering != null) {
                list = socialsReordering;
              } else {
                list = data;
              }
              return ReorderableList(
                padding: const EdgeInsets.only(top: 8.0),
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                itemExtent: 64.0,
                itemCount: list.length,
                itemBuilder: (context, index) {
                  final social = list[index];
                  return ReorderableDelayedDragStartListener(
                    enabled: list.length > 1,
                    index: index,
                    key: ValueKey('social-reorderable-${social.id}'),
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 8.0),
                      child: ProviderScope(
                        overrides: [
                          _socialItemProvider.overrideWithValue(social),
                        ],
                        child: const _SocialItem(),
                      ),
                    ),
                  );
                },
                proxyDecorator: (child, index, animation) {
                  final social = list[index];
                  return ScaleTransition(
                    scale: animation.drive(Tween(begin: 1.0, end: 1.06)),
                    child: ProviderScope(
                      overrides: [
                        _socialItemProvider.overrideWithValue(social),
                      ],
                      child: const _SocialItem(),
                    ),
                  );
                },
                onReorder: (int oldIndex, int newIndex) async {
                  if (oldIndex == newIndex) {
                    return;
                  }

                  final newList = list.toList();
                  final item = newList.removeAt(oldIndex);
                  newList.insert(
                    newIndex - oldIndex > 0 ? newIndex - 1 : newIndex,
                    item,
                  );
                  ref.read(_socialsReorderingListProvider.notifier).state = newList;
                  final reorderedMap = Map.fromEntries(
                    newList.mapIndexed(
                      (i, e) => MapEntry<String, int>(e.id.toString(), newList.length - i),
                    ),
                  );
                  // Cancel the previous sort request.
                  _cancelToken?.cancel();
                  final cancelToken = _cancelToken = CancelToken();
                  await ref
                      .read(apiServiceProvider)
                      .socialsReorder(
                        idInOrders: reorderedMap,
                        cancelToken: cancelToken,
                      );
                  if (mounted) {
                    final _ = await ref.refresh(fetchSocialsProvider().future);
                  }
                  if (mounted) {
                    ref.read(_socialsReorderingListProvider.notifier).state = null;
                  }
                },
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSocialsPlaceholder(BuildContext context) {
    final platforms = SocialPlatform.values.where((o) => o.events == null);
    return Container(
      margin: const EdgeInsets.only(top: 16),
      height: 40,
      child: Stack(
        children: List.generate(
          platforms.length.min(8),
          (index) => Positioned(
            left: index * 28.0,
            child: SocialSvgIcon(
              platform: platforms.elementAt(index),
              width: 44.0,
              height: 40.0,
              clipOval: false,
            ),
          ),
        ),
      ),
    );
  }
}

final _socialItemProvider = Provider.autoDispose<Social>(
  (ref) => throw UnimplementedError(),
);

class _SocialItem extends ConsumerWidget {
  const _SocialItem();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final social = ref.watch(_socialItemProvider);
    final currentPlatform = SocialPlatform.values.firstWhere(
      (option) => option.name == social.platformName,
      orElse: () => SocialPlatform.values.first,
    );

    final socialsResult = ref.watch(fetchSocialsProvider());
    final socials = socialsResult.valueOrNull ?? <Social>[];

    return RippleTap(
      onTap: () {
        meNavigator.pushNamed(
          Routes.socialPlatform.name,
          arguments: Routes.socialPlatform.d(
            social: social,
            platform: currentPlatform,
          ),
        );
      },
      padding: const EdgeInsets.symmetric(vertical: 2),
      height: 56,
      borderRadius: BorderRadius.circular(16),
      color: ColorName.primaryTextColorLight,
      child: Row(
        spacing: 8.0,
        children: [
          if (socials.length > 1)
            Container(
              width: 20.0,
              padding: const EdgeInsetsDirectional.only(start: 10.0),
              child: Assets.icons.dragHandle.svg(colorFilter: Colors.grey.filter),
            )
          else
            const Gap.h(2.0),
          SocialSvgIcon(
            platform: currentPlatform,
            width: 32.0,
            height: 32.0,
            clipOval: true,
          ),
          Expanded(
            child: Text(
              social.platformName == 'Whatsapp' || social.platformName == 'MemeX'
                  ? social.platformName
                  : social.handleName.replaceAll('tel:+', '').replaceAll('mailto:', ''),
              style: const TextStyle(
                fontSize: 18,
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (social.isVerify == true)
            Padding(
              padding: const EdgeInsetsDirectional.only(end: 12),
              child: Assets.icons.verified.svg(width: 24, height: 24),
            ),
        ],
      ),
    );
  }
}

class _SocialDialog extends ConsumerWidget {
  const _SocialDialog();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.all(16.0).copyWith(bottom: 0.0),
      child: Column(
        mainAxisSize: MainAxisSize.min, // 不要占用全部空间
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Add Links',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                icon: const Icon(Icons.close, color: Colors.black, size: 24),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
          const SizedBox(height: 24),
          const Expanded(child: SocialGrid()),
        ],
      ),
    );
  }
}

class _CardSheet extends ConsumerWidget {
  const _CardSheet({required this.cards});

  final List<CardInfo> cards;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Theme(
      data: themeBy(
        meTheme: defaultMEThemeDark,
        locale: Localizations.localeOf(context),
      ),
      child: _CardSheetBody(cards: cards),
    );
  }
}

class _CardSheetBody extends StatelessWidget {
  const _CardSheetBody({required this.cards});

  final List<CardInfo> cards;

  @override
  Widget build(BuildContext context) {
    // 按nfcType分组卡片，并使NFC424组在前面
    final type424 = <CardInfo>[];
    final type215 = <CardInfo>[];
    for (final card in cards) {
      if (card.nfcType == NfcType.NFC424) {
        type424.add(card);
      } else if (card.nfcType == NfcType.NFC215) {
        type215.add(card);
      }
    }

    return Padding(
      padding: const EdgeInsets.all(16.0).copyWith(bottom: 0.0),
      child: Column(
        spacing: 24.0,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'My Card3 Collections',
                style: context.textTheme.bodyMedium?.copyWith(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                icon: const Icon(Icons.close, size: 28),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                // NFC424卡片组 - Earn Points with Every Use
                if (type424.isNotEmpty) ...[
                  _buildTitle(context, 'V2.0'),
                  const SizedBox(height: 16),
                  _buildCardGrid(type424),
                  if (type215.isNotEmpty)
                    const Divider(
                      height: 32,
                      thickness: 1,
                      color: Color(0xFF4A4A59),
                    ),
                ],

                // NFC215卡片组 - Non-Reward
                if (type215.isNotEmpty) ...[
                  _buildTitle(context, 'V1.0'),
                  const SizedBox(height: 16),
                  _buildCardGrid(type215),
                ],

                Gap.bottomPadding(context, 50.0),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTitle(BuildContext context, String title) {
    return Text(
      title,
      style: context.textTheme.headlineSmall,
    );
  }

  // 构建卡片网格
  Widget _buildCardGrid(List<CardInfo> cards) {
    return GridView.builder(
      padding: EdgeInsets.zero,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 0.56, // 调整卡片比例更接近参考图
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: cards.length,
      itemBuilder: (context, index) {
        final card = cards[index];
        final paddedId = card.id.toString().padLeft(8, '0');

        return Column(
          spacing: 8.0,
          children: [
            // 卡片图像容器
            Expanded(
              child: Center(
                child: MEImage(
                  card.backCover,
                  fit: BoxFit.cover,
                  borderRadius: 12.0.rCircular,
                  emptyBuilder: (context) => switch (card.cardType) {
                    CardType.STICKER => Assets.icons.images.stickerCover.image(
                      fit: BoxFit.cover,
                    ),
                    CardType.WRISTBAND => Assets.icons.images.wristbandCover.image(
                      fit: BoxFit.cover,
                    ),
                    _ => Assets.icons.images.normalBackcover.image(
                      fit: BoxFit.cover,
                    ),
                  },
                ),
              ),
            ),
            // 卡片编号
            Text(
              '${paddedId.substring(0, 4)} ${paddedId.substring(paddedId.length - 4)}',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.white,
              ),
            ),
          ],
        );
      },
    );
  }
}
