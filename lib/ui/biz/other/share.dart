import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '/models/card.dart' show QRCodeDynamicResult;
import '/provider/api.dart' show apiServiceProvider;
import '/provider/user.dart';
import 'qr_expire_border.dart';

final _codeProvider = FutureProvider.autoDispose<QRCodeDynamicResult>((ref) async {
  final ct = ref.cancelToken();
  ref.onDispose(() {
    ct.cancel();
  });
  return ref.read(apiServiceProvider).getUserQRCodeDynamic(cancelToken: ct);
});

@FFRoute(name: '/share')
class SharePage extends StatelessWidget {
  const SharePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: themeBy(meTheme: defaultMEThemeLight, locale: Localizations.localeOf(context)),
      child: const _MainBody(),
    );
  }
}

class _MainBody extends ConsumerStatefulWidget {
  const _MainBody();

  @override
  ConsumerState<_MainBody> createState() => _MainBodyState();
}

class _MainBodyState extends ConsumerState<_MainBody> {
  Timer? _refreshTimer;
  late Timer _elapsedTimer;

  @override
  void initState() {
    super.initState();
    _initialize(refresh: false);
    _elapsedTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      final result = ref.read(_codeProvider).valueOrNull;
      if (result == null) {
        return;
      }

      safeSetState(() {});
    });
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    _elapsedTimer.cancel();
    super.dispose();
  }

  Future<void> _initialize({required bool refresh}) async {
    _refreshTimer?.cancel();

    final QRCodeDynamicResult result;
    if (refresh) {
      result = await ref.refresh(_codeProvider.future);
    } else {
      result = await ref.read(_codeProvider.future);
    }

    if (!mounted) {
      return;
    }

    final elapsed = result.expireTime.difference(DateTime.now()) - 100.milliseconds;
    if (elapsed <= Duration.zero) {
      _initialize(refresh: true);
      return;
    }

    _refreshTimer = Timer(elapsed, () => _initialize(refresh: true));
  }

  @override
  Widget build(BuildContext context) {
    final localUser = ref.watch(userRepoProvider);
    final userInfoResult = ref.watch(fetchUserInfoProvider());
    final userInfo = userInfoResult.valueOrNull ?? localUser;
    final codeResult = ref.watch(_codeProvider);

    final backgroundColorFilter = Colors.grey.withValues(alpha: 0.1).filter;

    return AppScaffold(
      backgroundBuilder: (context) => ListView.builder(
        itemBuilder: (context, index) => Padding(
          padding: const EdgeInsets.only(bottom: 48.0),
          child: AspectRatio(
            aspectRatio: 963 / 200,
            child: Assets.icons.logoTextDark.svg(fit: BoxFit.fitWidth, colorFilter: backgroundColorFilter),
          ),
        ),
      ),
      backgroundColor: Colors.grey[900],
      bodyPadding: const EdgeInsets.all(16.0),
      body: Center(
        child: Container(
          width: double.infinity,
          margin: const EdgeInsets.only(bottom: 48.0),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: context.theme.cardColor,
            borderRadius: BorderRadius.circular(16.0),
          ),
          child: Column(
            spacing: 16.0,
            mainAxisSize: MainAxisSize.min,
            children: [
              MEImage(
                userInfo?.avatar ?? '',
                width: 120.0,
                height: 120.0,
                fit: BoxFit.cover,
                clipOval: true,
                emptyBuilder: (context) => Icon(
                  Icons.account_circle,
                  size: 120.0,
                  color: Colors.grey.shade400,
                ),
              ),
              Text(
                userInfo?.name ?? '',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Container(
                width: 240.0,
                height: 280.0,
                margin: const EdgeInsets.symmetric(vertical: 16.0),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12.0),
                  color: Colors.grey[300]!,
                ),
                child: Builder(
                  builder: (context) {
                    if (codeResult.valueOrNull case final data?) {
                      final difference = data.expireTime.difference(DateTime.now());
                      return QrExpireBorder(
                        expireTime: data.expireTime,
                        borderWidth: 6.0,
                        borderColor: context.themeColor,
                        borderBackgroundColor: Colors.grey[300]!,
                        child: DecoratedBox(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(6.0),
                            color: Colors.white,
                          ),
                          child: Column(
                            children: [
                              AspectRatio(
                                aspectRatio: 1.0,
                                child: Stack(
                                  fit: StackFit.expand,
                                  children: [
                                    Tapper(
                                      onLongPress: () {
                                        copyAndToast(data.url);
                                      },
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(6.0),
                                        child: QrImageView(
                                          backgroundColor: Colors.white,
                                          data: data.url,
                                          version: QrVersions.auto,
                                          size: 200.0 - 12.0, // 留出边框宽度
                                        ),
                                      ),
                                    ),
                                    if (codeResult.isLoading) const AppLoading(size: 72.0),
                                  ],
                                ),
                              ),
                              Expanded(
                                child: FittedBox(
                                  fit: BoxFit.scaleDown,
                                  child: PlaceholderText(
                                    'QR code refreshes in **'
                                    '${(difference.inSeconds + 1).max(0)}s'
                                    '**',
                                    style: const TextStyle(fontSize: 12.0),
                                    matchedStyle: const TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                ),
                              ),
                              const Gap.v(6.0),
                            ],
                          ),
                        ),
                      );
                    }
                    if (codeResult.hasError && !codeResult.isLoading) {
                      return IconButton(
                        onPressed: () => ref.refresh(_codeProvider),
                        icon: Icon(
                          Icons.refresh_rounded,
                          color: context.meTheme.failingColor,
                          size: 72.0,
                        ),
                      );
                    }
                    return const AppLoading();
                  },
                ),
              ),
              // if (codeResult.valueOrNull != null)
              //   Tapper(
              //     onTap: () {
              //       copyAndToast(codeResult.valueOrNull?.url);
              //     },
              //     child: Container(
              //       padding: const EdgeInsets.symmetric(
              //         horizontal: 16,
              //         vertical: 12,
              //       ),
              //       decoration: BoxDecoration(
              //         borderRadius: BorderRadius.circular(8.0),
              //         color: context.theme.dividerColor,
              //       ),
              //       child: Row(
              //         spacing: 8.0,
              //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //         children: [
              //           Expanded(
              //             child: Text(
              //               codeResult.valueOrNull?.url ?? '',
              //               overflow: TextOverflow.ellipsis,
              //             ),
              //           ),
              //           const Icon(Icons.copy, size: 20),
              //         ],
              //       ),
              //     ),
              //   ),
            ],
          ),
        ),
      ),
    );
  }
}
