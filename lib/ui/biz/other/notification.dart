import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/models/business.dart' show Message, MessageType, MessageSayHiParams, Network, ImageAITaskDone;
import '/models/user.dart' show UserFromRelationType;
import '/provider/api.dart' show apiServiceProvider;
import '/provider/settings.dart' show selectedIndexProvider;
import '/provider/user.dart' show fetchUserInfoProvider;

final _listProvider = FutureProvider.autoDispose.family<Paged<Message>, int>((ref, page) {
  final ct = ref.cancelToken();
  ref.onDispose(() {
    ct.cancel();
  });
  return ref.read(apiServiceProvider).listMessages(page: page, cancelToken: ct);
});

@FFRoute(name: '/notification')
class NotificationPage extends ConsumerStatefulWidget {
  const NotificationPage({super.key});

  @override
  ConsumerState<NotificationPage> createState() => _NotificationPageState();
}

class _NotificationPageState extends ConsumerState<NotificationPage> {
  @override
  void initState() {
    super.initState();
    ref.read(_listProvider(1).future).then((_) {
      if (mounted) {
        ref.invalidate(fetchUserInfoProvider());
      }
    });
  }

  Future<void> _onRefresh() async {
    ref.invalidate(_listProvider);
    await ref.read(_listProvider(1).future);
  }

  @override
  Widget build(BuildContext context) {
    final totalResult = ref.watch(_listProvider(1));
    final Widget child;
    if (totalResult.valueOrNull?.total == 0) {
      child = RefreshableEmptyView(
        onTap: _onRefresh,
        message: 'No notifications yet.',
      );
    } else if (totalResult.hasError && !totalResult.isLoading) {
      final e = totalResult.error;
      child = RefreshableEmptyView(
        onTap: _onRefresh,
        message: isNetworkError(e) ? context.l10nME.networkError : '$e\n${context.l10nME.clickToRetryButton}',
      );
    } else {
      const size = 20;
      child = ListView.builder(
        padding: const EdgeInsets.symmetric(vertical: 14.0),
        itemCount: totalResult.valueOrNull?.total ?? 4,
        itemBuilder: (context, index) {
          final page = index ~/ size + 1;
          final indexInPage = index % size;
          final result = ref.watch(_listProvider(page));
          return result.maybeWhen(
            data: (data) {
              if (indexInPage >= data.list.length) {
                return null;
              }
              final item = data.list[indexInPage];
              final jsonParams = jsonDecode(item.params.or('{}'));
              return ProviderScope(
                overrides: [
                  _itemProvider.overrideWithValue((item, jsonParams)),
                ],
                child: const _MessageItem(),
              );
            },
            orElse: () => const _MessageItemShimmer(),
          );
        },
      );
    }
    return AppScaffold(
      title: 'Notifications',
      body: RefreshIndicator(onRefresh: _onRefresh, child: child),
    );
  }
}

final _itemProvider = Provider.autoDispose<(Message, Map<String, dynamic>)>((ref) => throw UnimplementedError());

class _MessageItem extends ConsumerWidget {
  const _MessageItem();

  void _switchNetwork(BuildContext context, WidgetRef ref, Network network) {
    AppLoading.run(() => ChainManager.instance.switchNetwork(network));
  }

  // 从通知消息中提取内容
  Map<String, String> extractBracketContent(String content) {
    final RegExp bracketRegex = RegExp(r'\[(.*?)\]');
    final match = bracketRegex.firstMatch(content);
    String? extractedContent;
    String cleanedText = content;

    if (match != null) {
      extractedContent = match.group(1);
      cleanedText = content.replaceAll(bracketRegex, '').trim();
    }

    return {
      'extractedContent': extractedContent ?? '',
      'cleanedText': cleanedText,
    };
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final (message, jsonParams) = ref.watch(_itemProvider);
    return RippleTap(
      onTap: () async {
        if (message.messageType == MessageType.sayHi) {
          meNavigator.pushNamed(
            Routes.funConnection.name,
            arguments: Routes.funConnection.d(type: UserFromRelationType.follower),
          );
          return;
        }

        if (message.messageType == MessageType.follow) {
          final params = MessageSayHiParams.fromJson(jsonParams);
          meNavigator.pushNamed(
            Routes.socialProfile.name,
            arguments: Routes.socialProfile.d(code: params.referralCode),
          );
          return;
        }

        if (message.messageType == MessageType.airdrop) {
          ref.read(selectedIndexProvider.notifier).state = 2;

          final chainId = jsonParams['chainId'];
          final networks = BoxService.getNetworksFromLocal();
          final network = networks.firstWhere(
            (network) => network.chainIdEvm == chainId,
            orElse: () => networks.first,
          );
          _switchNetwork(context, ref, network);
          meNavigator.popAndPushNamed(Routes.home.name);
          return;
        }

        if (message.messageType == MessageType.integral) {
          meNavigator.pushNamed(Routes.funPointRecord.name);
          return;
        }

        if (message.messageType == MessageType.aiTaskImage) {
          final taskResult = ImageAITaskDone.fromJson(jsonParams);
          meNavigator.removeRouteByName(Routes.socialEditProfile.name);
          meNavigator.removeRouteByName(Routes.aiGenerateImage.name);
          final result = await meNavigator.pushNamed(
            Routes.aiGenerateImage.name,
            arguments: Routes.aiGenerateImage.d(
              taskId: taskResult.taskId,
              generatedUrl: taskResult.imageUrl,
            ),
          );
          if (result is! String) {
            return;
          }
          meNavigator.pushNamed(
            Routes.socialEditProfile.name,
            arguments: Routes.socialEditProfile.d(pendingAvatarUrl: result),
          );
          return;
        }

        if (message.messageType == MessageType.text) {
          return;
        }
      },
      margin: const EdgeInsets.only(bottom: 12),
      borderRadius: BorderRadius.circular(16),
      padding: const EdgeInsets.all(16),
      color: context.theme.cardColor,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconTheme(
                data: context.iconTheme.copyWith(color: context.textTheme.bodyMedium?.color),
                child: _buildIcon(context, message.messageType),
              ),
              Text(
                message.createTime.withDateTimeFormat(format: 'yyyy-MM-dd HH:mm'),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          _buildContent(context, message, jsonParams),
        ],
      ),
    );
  }

  // 根据消息类型渲染不同的图标
  Widget _buildIcon(BuildContext context, MessageType messageType) {
    switch (messageType) {
      case MessageType.push:
        return const Icon(
          Icons.chat_outlined,
          size: 24,
        );
      case MessageType.sayHi:
        return const Icon(
          Icons.waving_hand_outlined,
          size: 24,
        );
      case MessageType.follow:
        return const Icon(
          Icons.supervised_user_circle_outlined,
          size: 24,
        );
      case MessageType.airdrop:
        return const Icon(
          Icons.paid_outlined,
          size: 24,
        );
      case MessageType.integral:
        return const Icon(
          Icons.redeem_outlined,
          size: 24,
        );
      case MessageType.aiTaskImage:
        return const Icon(
          Icons.image_outlined,
          size: 24,
        );
      default:
        return const Icon(
          Icons.notifications_outlined,
          size: 24,
        );
    }
  }

  // 根据消息类型渲染内容
  Widget _buildContent(
    BuildContext context,
    Message message,
    Map<String, dynamic> jsonParams,
  ) {
    final content = message.message;
    final primaryColor = context.themeColor;

    switch (message.messageType) {
      case MessageType.sayHi:
      case MessageType.follow:
        final params = MessageSayHiParams.fromJson(jsonParams);
        return Padding(
          padding: const EdgeInsets.only(top: 12.0),
          child: Column(
            spacing: 16.0,
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                message.messageType == MessageType.sayHi ? 'Say HI from ${params.name}' : content,
                style: const TextStyle(fontSize: 16),
              ),
              SizedBox(
                height: 60.0,
                child: Row(
                  spacing: 10.0,
                  children: [
                    UserAvatar(user: params, dimension: 60.0),
                    Column(
                      spacing: 2.0,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          params.name.or('?'),
                          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        Flexible(
                          child: Text(
                            params.title.or(params.company).or(params.email).or('?'),
                            style: context.textTheme.bodySmall,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      case MessageType.push:
        final extracted = extractBracketContent(content);
        return Padding(
          padding: const EdgeInsets.only(top: 8.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                extracted['cleanedText']!,
                style: const TextStyle(fontSize: 16),
              ),
              if (extracted['extractedContent']!.isNotEmpty)
                Row(
                  children: [
                    Text(
                      '${extracted['extractedContent']!.split('|')[0]} :',
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(width: 4),
                    GestureDetector(
                      onTap: () {
                        // 打开链接
                        // TODO: 实现链接跳转
                      },
                      child: Text(
                        extracted['extractedContent']!.split('|')[1],
                        style: TextStyle(
                          fontSize: 16,
                          color: primaryColor,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                  ],
                ),
            ],
          ),
        );

      case MessageType.airdrop:
      case MessageType.integral:
        return Padding(
          padding: const EdgeInsets.only(top: 12.0),
          child: Column(
            spacing: 16.0,
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(content, style: const TextStyle(fontSize: 16)),
              if (message.messageType == MessageType.integral)
                _buildButton(
                  context,
                  'View',
                  () => Navigator.of(context).pushNamed(Routes.funPointRecord.name),
                ),
            ],
          ),
        );

      case MessageType.aiTaskImage:
        final params = ImageAITaskDone.fromJson(jsonParams);
        return Padding(
          padding: const EdgeInsets.only(top: 8.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 6.0,
            children: [
              Text(content, style: const TextStyle(fontSize: 16)),
              if (params.imageUrl.isNotEmpty)
                MEImage(
                  params.imageUrl,
                  width: 100.0,
                  height: 100.0,
                  cacheWidth: 100.toCache(context),
                  borderRadius: BorderRadius.circular(10.0),
                  fit: BoxFit.cover,
                  emptyBuilder: (context) => const Center(
                    child: Icon(
                      Icons.question_mark_rounded,
                      size: 48.0,
                    ),
                  ),
                ),
            ],
          ),
        );
      default:
        return Padding(
          padding: const EdgeInsets.only(top: 8.0),
          child: Text(content, style: const TextStyle(fontSize: 16)),
        );
    }
  }

  Widget _buildButton(
    BuildContext context,
    String text,
    VoidCallback onPressed,
  ) {
    return ThemeTextButton.outlined(
      onPressed: onPressed,
      width: null,
      height: 36.0,
      intrinsicWidth: true,
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      text: text,
      textStyle: const TextStyle(fontSize: 14.0),
    );
  }
}

class _MessageItemShimmer extends StatelessWidget {
  const _MessageItemShimmer();

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: context.theme.cardColor,
      ),
      child: MEShimmer(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 10.0,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Icon(Icons.message, size: 24.0),
                Container(width: 100.0, height: 12.0, color: context.theme.cardColor),
              ],
            ),
            Container(
              width: 100.0,
              height: 14.0,
              color: context.theme.dividerColor,
            ),
          ],
        ),
      ),
    );
  }
}
