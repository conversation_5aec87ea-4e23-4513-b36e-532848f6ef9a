import 'dart:async';
import 'dart:math' as math;

import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show SystemChannels;
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:url_launcher/url_launcher.dart';

import '/feat/bridge/module/in_house.dart';
import '/feat/bridge/module/launch.dart' show askToLaunchUrlExternally;
import '/feat/link/handler.dart' show SchemeLinkHandler;
import '/feat/link/helper.dart' show AppLinkHelper;

const _tag = '🌐 WebView';

@FFRoute(name: '/webview')
class WebViewPage extends ConsumerStatefulWidget {
  const WebViewPage({
    super.key,
    required this.url,
    this.title,
  });

  final String url;
  final String? title;

  @override
  ConsumerState<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends ConsumerState<WebViewPage> with WidgetsBindingObserver {
  final progressController = StreamController<double>.broadcast();
  late final _titleNotifier = ValueNotifier<String?>(widget.title?.trim());
  late final _currentUrl = ValueNotifier(widget.url.trim());

  String? get urlDomain => Uri.tryParse(_currentUrl.value)?.host;

  Timer? _progressCancelTimer;

  InAppWebViewController? get _webViewController {
    if (mounted) {
      return _inAppWebViewController;
    }
    return null;
  }

  InAppWebViewController? _inAppWebViewController;

  bool _canWebViewGoBack = false;
  bool _isLoading = true;
  String? _errorMessage;

  // 添加超时定时器
  Timer? _loadingTimeout;
  Completer<void>? _closingLock;

  // 添加应用生命周期状态追踪
  bool _isAppInBackground = false;
  DateTime? _backgroundTime;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // 设置30秒超时
    _loadingTimeout = Timer(const Duration(seconds: 30), () {
      if (_isLoading) {
        safeSetState(() {
          _isLoading = false;
          _errorMessage = 'Page load timeout (30s)';
        });
      }
    });
  }

  @override
  void dispose() {
    SystemChannels.textInput.invokeMethod<void>('TextInput.hide');
    WidgetsBinding.instance.removeObserver(this);
    progressController.close();
    _loadingTimeout?.cancel();
    _webViewController?.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
        if (!_isAppInBackground) {
          // 避免重复设置
          _isAppInBackground = true;
          _backgroundTime = DateTime.now();
        }

      case AppLifecycleState.resumed:
        if (_isAppInBackground && _backgroundTime != null) {
          final backgroundDuration = DateTime.now().difference(_backgroundTime!);
          // 缩短时间阈值到5秒，让恢复更及时
          if (backgroundDuration.inSeconds >= 60) {
            _checkWebViewStateAfterResume();
          }

          // 重置状态
          _isAppInBackground = false;
          _backgroundTime = null;
        } else {
          // 确保状态正确重置
          _isAppInBackground = false;
          _backgroundTime = null;
        }
      default:
        break;
    }
  }

  void cancelProgress([Duration duration = const Duration(seconds: 1)]) {
    _progressCancelTimer?.cancel();
    _progressCancelTimer = Timer(duration, () {
      if (!progressController.isClosed) {
        progressController.add(0.0);
      }
    });
  }

  URLRequest _getRequestByUrl(String url, {required bool withNoCache}) {
    return URLRequest(
      url: WebUri(url),
      headers: {
        'User-Agent': PackageUtil.userAgent,
        if (withNoCache) 'Cache-Control': 'no-cache, no-store, must-revalidate',
        if (withNoCache) 'Pragma': 'no-cache',
        if (withNoCache) 'Expires': '0',
      },
    );
  }

  // 检查WebView状态并在必要时重新加载
  Future<void> _checkWebViewStateAfterResume() async {
    if (!mounted || _webViewController == null) {
      return;
    }
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      if (!mounted || _webViewController == null) {
        return;
      }
      await _reloadWebView();
    } catch (e) {
      safeSetState(() {
        _isLoading = false;
        _errorMessage = 'WebView recovery failed: $e';
      });
      rethrow;
    }
  }

  // 重新加载WebView
  Future<void> _reloadWebView() async {
    if (!mounted || _webViewController == null) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // 添加5秒超时
      await _performReload().timeout(const Duration(seconds: 5));
    } catch (e) {
      safeSetState(() {
        _isLoading = false;
        _errorMessage = 'Failed to reload page: $e';
      });
      rethrow;
    }
  }

  // 执行实际的重新加载操作
  Future<void> _performReload() async {
    final controller = _webViewController;
    if (controller == null) {
      return;
    }
    await controller.loadUrl(
      urlRequest: _getRequestByUrl(_currentUrl.value, withNoCache: true),
    );
  }

  void _onWebViewCreated(InAppWebViewController controller) {
    _inAppWebViewController = controller;
    defaultInHouseBridge.inject(context, ref, controller);
    controller.addJavaScriptHandler(
      handlerName: '__onTitleChanged__',
      callback: (args) {
        final title = args.firstOrNull;
        if (title != null) {
          _titleNotifier.value = title;
        }
      },
    );
  }

  Future<void> _onLoadStart(
    InAppWebViewController controller,
    WebUri? url,
  ) async {
    safeSetState(() {
      if (url?.toString() case final url?) {
        _currentUrl.value = url;
      }
      _isLoading = true;
      _errorMessage = null;
    });
  }

  Future<void> _onLoadStop(
    InAppWebViewController controller,
    WebUri? url,
  ) async {
    if (!mounted) {
      return;
    }
    if (url?.toString() case final url?) {
      _currentUrl.value = url;
    }
    _isLoading = false;
    _loadingTimeout?.cancel();

    if (url?.host case final host? when defaultInHouseBridgeHostRegExp.hasMatch(host)) {
      final token = BoxService.getToken() ?? '';
      await controller.evaluateJavascript(
        source: inHouseJSSourceInjectToken(token),
      );
    }
  }

  Future<void> _onCloseWindow(InAppWebViewController controller) {
    if (_closingLock case final lock?) {
      return lock.future;
    }

    final lock = _closingLock = Completer<void>();
    TinyDialog.show<bool>(
          text: 'Are you sure you want to close this page?',
          buttons: (context) => [
            Row(
              spacing: 10.0,
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).maybePop(false),
                  child: Text(context.l10nME.laterButton),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).maybePop(true),
                  child: Text(context.l10nME.closeButton),
                ),
              ],
            ),
          ],
        )
        .then(
          (result) {
            lock.complete();
            if (result == true && mounted) {
              controller.stopLoading();
              Navigator.of(context).maybePop();
            }
          },
          onError: (e, s) {
            lock.completeError(e, s);
          },
        )
        .whenComplete(() {
          _closingLock = null;
        });
    return lock.future;
  }

  Future<PermissionResponse> _onPermissionRequest(
    InAppWebViewController controller,
    PermissionRequest request,
  ) async {
    LogUtil.d('WebView permission request: ${request.resources}', tag: _tag);

    // Map WebView permission resources to system permissions
    final permissions = <Permission>[];

    for (final resource in request.resources) {
      if (resource == PermissionResourceType.CAMERA_AND_MICROPHONE) {
        permissions.add(Permission.camera);
        permissions.add(Permission.microphone);
      } else if (resource == PermissionResourceType.CAMERA) {
        permissions.add(Permission.camera);
      } else if (resource == PermissionResourceType.MICROPHONE) {
        permissions.add(Permission.microphone);
      } else if (resource == PermissionResourceType.GEOLOCATION) {
        permissions.add(Permission.location);
      } else {
        LogUtil.w('Unknown permission resource: $resource', tag: _tag);
      }
    }

    if (permissions.isEmpty) {
      // If no mappable permissions, grant all by default
      return PermissionResponse(
        resources: request.resources,
        action: PermissionResponseAction.GRANT,
      );
    }

    try {
      // Check current permission status
      final status = await permissions.request();

      // Check if all permissions are already granted
      final allGranted = status.values.every((status) => status.isGranted);

      if (allGranted) {
        return PermissionResponse(
          resources: request.resources,
          action: PermissionResponseAction.GRANT,
        );
      }

      // Request permissions that are not granted
      final notGrantedPermissions = status.entries
          .where((entry) => !entry.value.isGranted && !entry.value.isPermanentlyDenied)
          .map((entry) => entry.key)
          .toList();

      if (notGrantedPermissions.isNotEmpty) {
        final requestResult = await notGrantedPermissions.request();

        // Check if all requested permissions are now granted
        final allRequestedGranted = requestResult.values.every((s) => s.isGranted);

        if (allRequestedGranted) {
          return PermissionResponse(
            resources: request.resources,
            action: PermissionResponseAction.GRANT,
          );
        } else {
          // Check if any permission is permanently denied
          final hasPermanentlyDenied = requestResult.values.any((status) => status.isPermanentlyDenied);

          if (hasPermanentlyDenied) {
            // Show dialog to guide user to app settings
            final shouldOpenSettings = await _showPermissionDeniedDialog();
            if (shouldOpenSettings == true) {
              await openAppSettings();
            }
          }

          return PermissionResponse(
            resources: request.resources,
            action: PermissionResponseAction.DENY,
          );
        }
      } else {
        // All permissions are either granted or permanently denied
        final hasPermanentlyDenied = status.values.any((status) => status.isPermanentlyDenied);

        if (hasPermanentlyDenied) {
          final shouldOpenSettings = await _showPermissionDeniedDialog();
          if (shouldOpenSettings == true) {
            await openAppSettings();

            // Re-check permissions after potentially changing settings
            final recheckResult = <Permission, PermissionStatus>{
              for (final permission in permissions) permission: await permission.status,
            };
            final allGrantedAfterSettings = recheckResult.values.every((status) => status.isGranted);

            return PermissionResponse(
              resources: request.resources,
              action: allGrantedAfterSettings ? PermissionResponseAction.GRANT : PermissionResponseAction.DENY,
            );
          }
        }

        return PermissionResponse(
          resources: request.resources,
          action: PermissionResponseAction.DENY,
        );
      }
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s, tag: _tag, tagWithTrace: false);
      // On error, deny the permission to be safe
      return PermissionResponse(
        resources: request.resources,
        action: PermissionResponseAction.DENY,
      );
    }
  }

  // Show dialog when permissions are permanently denied
  Future<bool?> _showPermissionDeniedDialog() async {
    if (!mounted) {
      return null;
    }

    return TinyDialog.show<bool>(
      text:
          'Permissions are required for this webpage to function properly. '
          'Please grant the necessary permissions in app settings.',
      buttons: (context) => [
        Row(
          spacing: 10.0,
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            TextButton(
              onPressed: () => Navigator.of(context).maybePop(false),
              child: Text(context.l10nME.laterButton),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).maybePop(true),
              child: const Text('Open'),
            ),
          ],
        ),
      ],
    );
  }

  Future<NavigationActionPolicy> _shouldOverrideUrlLoading(
    InAppWebViewController controller,
    NavigationAction navigationAction,
  ) async {
    if (!mounted || _webViewController == null) {
      return NavigationActionPolicy.CANCEL;
    }

    final uri = navigationAction.request.url;
    if (uri == null) {
      return NavigationActionPolicy.CANCEL;
    }

    LogUtil.d(
      'shouldOverrideUrlLoading '
      '[url=${navigationAction.request.url?.rawValue}] '
      '[headers=${navigationAction.request.headers}]',
    );

    if (uri.scheme == AppLinkHelper.scheme) {
      final firstPath = uri.pathSegments.firstOrNull;
      if (firstPath == SchemeLinkHandler.methodWebview) {
        final url = (uri.queryParameters['url'] ?? '').trim();

        late final String decodedUrl;
        try {
          decodedUrl = Uri.decodeComponent(url);
        } catch (e, s) {
          handleExceptions(error: e, stackTrace: s);
          return NavigationActionPolicy.CANCEL;
        }

        final parsedUrl = Uri.tryParse(decodedUrl);
        if (parsedUrl == null || !parsedUrl.hasAbsolutePath) {
          return NavigationActionPolicy.CANCEL;
        }

        controller.loadUrl(
          urlRequest: _getRequestByUrl(
            parsedUrl.toString(),
            withNoCache: false,
          ),
        );
      }

      // Try to handles other card3:// link by the helper.
      await AppLinkHelper.handleUri(uri);

      // Do not handle it anymore, otherwise the app might be opened again.
      return NavigationActionPolicy.CANCEL;
    }

    // Determine if the link can be launched natively.
    if (!RegExp(r'https?').hasMatch(uri.scheme)) {
      if (await canLaunchUrl(uri)) {
        final launch = await askToLaunchUrlExternally();
        if (launch == true) {
          launchUrl(uri, mode: LaunchMode.externalApplication);
        }
      }

      // Cancel whether the link can be launched or not.
      return NavigationActionPolicy.CANCEL;
    }

    // Proceed with regular url.
    return NavigationActionPolicy.ALLOW;
  }

  void _onUpdateVisitedHistory(
    InAppWebViewController controller,
    WebUri? url,
    bool? isReload,
  ) {
    if (!mounted) {
      return;
    }
    LogUtil.d(
      'WebView onUpdateVisitedHistory: $url, $isReload',
      tag: _tag,
      tagWithTrace: false,
    );
    if (url?.toString() case final url?) {
      _currentUrl.value = url;
    }
    cancelProgress();
    routeDuration.delay.then((_) async {
      safeSetState(() {
        Future.wait<void>([
          controller.canGoBack().then((r) => _canWebViewGoBack = r, onError: (_) => false),
        ]);
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final Widget child;
    if (_errorMessage != null) {
      child = _buildErrorWidget();
    } else {
      child = SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: Stack(
          children: [
            // 确保WebView完全填充容器
            Positioned.fill(
              child: InAppWebView(
                initialSettings: InAppWebViewSettings(
                  allowsInlineMediaPlayback: true,
                  isInspectable: !Release.sealed,
                  applicationNameForUserAgent: PackageUtil.userAgent,
                  mediaPlaybackRequiresUserGesture: false,
                  builtInZoomControls: false,
                  javaScriptCanOpenWindowsAutomatically: true,
                  mixedContentMode: MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,
                  incognito: false,
                ),
                initialUrlRequest: _getRequestByUrl(widget.url, withNoCache: false),
                onWebViewCreated: _onWebViewCreated,
                onLoadStart: _onLoadStart,
                onLoadStop: _onLoadStop,
                onCloseWindow: _onCloseWindow,
                onPermissionRequest: _onPermissionRequest,
                shouldOverrideUrlLoading: _shouldOverrideUrlLoading,
                onUpdateVisitedHistory: _onUpdateVisitedHistory,
                onProgressChanged: (controller, progress) {
                  progressController.add(math.max(2, progress) / 100);
                },
                onRenderProcessUnresponsive: (controller, url) async {
                  await _reloadWebView();
                  return null;
                },
              ),
            ),
            Positioned.fill(
              bottom: null,
              child: StreamBuilder<double>(
                initialData: 0.0,
                stream: progressController.stream,
                builder: (_, data) => LinearProgressIndicator(
                  backgroundColor: Colors.transparent,
                  color: context.themeColor,
                  minHeight: 2,
                  value: data.data,
                ),
              ),
            ),
          ],
        ),
      );
    }
    return PopScope(
      canPop: _webViewController == null || !_canWebViewGoBack,
      onPopInvokedWithResult: (didPop, _) {
        if (didPop) {
          return;
        }
        _webViewController?.goBack();
      },
      child: AppScaffold(
        title: widget.title,
        titleStyle: const TextStyle(color: Colors.white),
        titleBuilder: (context) => Row(
          spacing: 4.0,
          children: [
            CloseButton(
              onPressed: () => Navigator.of(context).pop(),
            ),
            Expanded(
              child: ValueListenableBuilder(
                valueListenable: _titleNotifier,
                builder: (_, value, _) => Text(value ?? ''),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.black,
        bodyPadding: EdgeInsets.zero,
        appBarActions: [
          IconButton(
            onPressed: _reloadWebView,
            tooltip: 'Refresh page',
            icon: const Icon(Icons.refresh, color: Colors.white),
          ),
        ],
        onBackButtonPressed: () {
          if (_canWebViewGoBack) {
            _webViewController?.goBack();
          } else {
            Navigator.maybePop(context);
          }
        },
        body: child,
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            const Text(
              'Failed to load page',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: const TextStyle(
                color: Colors.red,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: () {
                    safeSetState(() {
                      _errorMessage = null;
                      _isLoading = true;
                    });
                    // 重新加载页面
                    _webViewController?.reload();
                  },
                  child: const Text('Retry'),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('Close'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
