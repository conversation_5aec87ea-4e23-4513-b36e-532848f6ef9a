import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

@FFAutoImport()
import '/models/user.dart' show UserFromRelation, UserFromRelationType;
import '/provider/api.dart' show apiServiceProvider;
import '/provider/user.dart';

final _selectedTypeProvider = StateProvider.autoDispose<UserFromRelationType>(
  (ref) => throw UnimplementedError(),
);

final _totalResultProvider = FutureProvider.autoDispose.family<int?, UserFromRelationType>(
  (ref, type) async {
    final result = await ref.watch(fetchUsersFromRelationProvider(type: type, page: 1).future);
    return result.total;
  },
);

@FFRoute(name: '/fun/connection')
class ConnectionPage extends StatefulWidget {
  const ConnectionPage({super.key, this.type});

  final UserFromRelationType? type;

  @override
  State<ConnectionPage> createState() => _ConnectionPageState();
}

class _ConnectionPageState extends State<ConnectionPage> {
  @override
  Widget build(BuildContext context) {
    return ProviderScope(
      overrides: [
        _selectedTypeProvider.overrideWith(
          (ref) => widget.type ?? UserFromRelationType.following,
        ),
      ],
      child: Theme(
        data: themeBy(
          meTheme: defaultMEThemeDark,
          locale: Localizations.localeOf(context),
        ),
        child: const _MainBody(),
      ),
    );
  }
}

class _MainBody extends ConsumerWidget {
  const _MainBody();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final type = ref.watch(_selectedTypeProvider);
    return AppScaffold(
      bodyPadding: const EdgeInsets.symmetric(horizontal: 12.0),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12.0),
            child: MESlidingSegmentedControl(
              children: {
                for (final e in UserFromRelationType.values)
                  e: Text(
                    () {
                      final total = ref.watch(_totalResultProvider(e));
                      final buffer = StringBuffer(e.displayName(context));
                      if (total.valueOrNull case final total? when total > 0) {
                        buffer.write(' (${total.toNumerical(fractionDigits: 1)})');
                      }
                      return buffer.toString();
                    }(),
                    style: TextStyle(
                      fontWeight: type == e ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
              },
              groupValue: type,
              padding: const EdgeInsets.symmetric(vertical: 3, horizontal: 4),
              backgroundColor: context.theme.cardColor,
              thumbColor: context.theme.scaffoldBackgroundColor,
              thumbRadius: const Radius.circular(14.0),
              cornerRadius: const Radius.circular(16.0),
              onValueChanged: (value) => ref.read(_selectedTypeProvider.notifier).state = value!,
            ),
          ),
          const Expanded(child: _List()),
        ],
      ),
    );
  }
}

class _List extends ConsumerStatefulWidget {
  const _List();

  @override
  ConsumerState<_List> createState() => _ListState();
}

class _ListState extends ConsumerState<_List> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  void dispose() {
    _userRelationFollowingPatch.clear();
    super.dispose();
  }

  Future<void> _onRefresh() async {
    final type = ref.read(_selectedTypeProvider);
    ref.invalidate(fetchUsersFromRelationProvider);
    await ref.read(
      fetchUsersFromRelationProvider(type: type, page: 1).future,
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final type = ref.watch(_selectedTypeProvider);
    final totalResult = ref.watch(fetchUsersFromRelationProvider(type: type, page: 1));
    final Widget child;
    if (totalResult.valueOrNull?.total == 0) {
      child = RefreshableEmptyView(
        onTap: _onRefresh,
        message: 'No connections yet.',
      );
    } else if (totalResult.hasError && !totalResult.isLoading) {
      final e = totalResult.error;
      child = RefreshableEmptyView(
        onTap: _onRefresh,
        message: isNetworkError(e) ? context.l10nME.networkError : '$e\n${context.l10nME.clickToRetryButton}',
      );
    } else {
      const size = 20;
      child = ListView.builder(
        padding: const EdgeInsets.symmetric(vertical: 14.0),
        itemCount: totalResult.valueOrNull?.total ?? 4,
        itemBuilder: (context, index) {
          final page = index ~/ size + 1;
          final indexInPage = index % size;
          final result = ref.watch(
            fetchUsersFromRelationProvider(type: type, page: page),
          );
          return result.maybeWhen(
            data: (data) {
              if (indexInPage >= data.list.length) {
                return null;
              }
              final item = data.list[indexInPage];
              return ProviderScope(
                overrides: [
                  _userItemProvider.overrideWithValue(item),
                ],
                child: const _UserItem(),
              );
            },
            orElse: () => const _UserItemShimmer(),
          );
        },
      );
    }
    return RefreshIndicator(onRefresh: _onRefresh, child: child);
  }
}

typedef _RelationFamily = (String, UserFromRelationType);

final _userItemProvider = Provider.autoDispose<UserFromRelation>(
  (ref) => throw UnimplementedError(),
);
final _userItemLoadingProvider = StateProvider.autoDispose.family<bool, _RelationFamily>(
  (ref, code) => false,
);
final _userRelationFollowingPatch = <_RelationFamily, bool>{};

class _UserItem extends ConsumerWidget {
  const _UserItem();

  Future<void> _toggleFollowing(
    BuildContext context,
    WidgetRef ref,
    bool following,
  ) async {
    final type = ref.read(_selectedTypeProvider);
    final item = ref.read(_userItemProvider);
    final family = (item.referralCode, type);
    final provider = _userItemLoadingProvider(family);

    ref.read(provider.notifier).state = true;
    try {
      await ref
          .read(apiServiceProvider)
          .toggleUserFollow(
            referralCode: item.referralCode,
            follow: following,
          );
      _userRelationFollowingPatch[family] = following;
    } finally {
      if (context.mounted) {
        ref.read(provider.notifier).state = false;
      }
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final item = ref.watch(_userItemProvider);
    final type = ref.watch(_selectedTypeProvider);
    final code = item.referralCode;
    final family = (code, type);
    final loading = ref.watch(_userItemLoadingProvider(family));
    final following = _userRelationFollowingPatch[family] ?? item.following;
    return RippleTap(
      onTap: () => Navigator.of(context).pushNamed(
        Routes.socialProfile.name,
        arguments: Routes.socialProfile.d(code: item.referralCode),
      ),
      height: 80.0,
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 10.0),
      child: Row(
        spacing: 10.0,
        children: [
          UserAvatar(user: item, dimension: 60.0),
          Expanded(
            child: Column(
              spacing: 4.0,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  spacing: 6.0,
                  children: [
                    Flexible(
                      child: Text(
                        item.name,
                        style: TextStyle(
                          fontSize: item.company.isEmpty && item.title.isEmpty ? 24.0 : 18.0,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (item.followedBy && type == UserFromRelationType.following)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 2.0),
                        decoration: BoxDecoration(
                          borderRadius: RadiusConstants.max,
                          color: context.meTheme.notificationColor,
                        ),
                        child: const Text(
                          'Following you',
                          style: TextStyle(color: Colors.black, fontSize: 10.0),
                        ),
                      ),
                  ],
                ),
                if (item.company.isNotEmpty || item.title.isNotEmpty)
                  Flexible(
                    child: Text(
                      () {
                        final buffer = StringBuffer();
                        if (item.company.isNotEmpty) {
                          buffer.write(item.company);
                          if (item.title.isNotEmpty) {
                            buffer.write('\n');
                          }
                        }
                        if (item.title.isNotEmpty) {
                          buffer.write(item.title);
                        }
                        return buffer.toString();
                      }(),
                      style: const TextStyle(color: Colors.grey),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
              ],
            ),
          ),
          AnimatedSwitcher(
            duration: kThemeAnimationDuration,
            switchInCurve: Curves.easeInOutCubic,
            switchOutCurve: Curves.easeInOutCubic,
            child: switch (loading) {
              true => const AppLoading(size: kMinInteractiveDimension),
              false => OutlinedButton(
                onPressed: () => _toggleFollowing(context, ref, !following),
                style: OutlinedButton.styleFrom(
                  backgroundColor: following ? context.theme.scaffoldBackgroundColor : Colors.white,
                  shape: const RoundedRectangleBorder(
                    borderRadius: RadiusConstants.max,
                  ),
                  side: BorderSide(color: context.theme.dividerColor),
                ),
                child: Text(
                  switch (type) {
                    UserFromRelationType.following => following ? 'Following' : 'Follow',
                    UserFromRelationType.follower => following ? 'Mutual Following' : 'Follow Back',
                  },
                  style: TextStyle(
                    color: following ? context.textTheme.bodyMedium?.color : Colors.black,
                    fontWeight: FontWeight.normal,
                  ),
                ),
              ),
            },
          ),
        ],
      ),
    );
  }
}

class _UserItemShimmer extends StatelessWidget {
  const _UserItemShimmer();

  @override
  Widget build(BuildContext context) {
    return MEShimmer(
      child: Container(
        height: 60.0,
        margin: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 10.0),
        child: Row(
          spacing: 10.0,
          children: [
            AspectRatio(
              aspectRatio: 1.0,
              child: CircleAvatar(backgroundColor: context.theme.dividerColor),
            ),
            Expanded(
              child: Column(
                spacing: 8.0,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 160.0,
                    height: 18.0,
                    color: context.theme.cardColor,
                  ),
                  Flexible(
                    child: Column(
                      spacing: 2.0,
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: 60.0,
                          height: 14.0,
                          color: context.theme.cardColor,
                        ),
                        Container(
                          width: 30.0,
                          height: 14.0,
                          color: context.theme.cardColor,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Container(
              width: 84.0,
              height: 36.0,
              decoration: BoxDecoration(
                borderRadius: RadiusConstants.max,
                color: context.theme.cardColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
