import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/models/business.dart';
import '/provider/api.dart' show apiServiceProvider;
import '/provider/user.dart';

@FFRoute(name: '/fun/point_record')
class PointRecord extends StatelessWidget {
  const PointRecord({super.key});

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: themeBy(
        meTheme: defaultMEThemeDark,
        locale: Localizations.localeOf(context),
      ),
      child: const _PointRecord(),
    );
  }
}

final _listProvider = FutureProvider.autoDispose.family<Paged<Point>, int>((ref, page) {
  if (page <= 0) {
    return Paged.empty(page: 0);
  }
  final ct = ref.cancelToken();
  ref.onDispose(() {
    ct.cancel();
  });
  return ref
      .read(apiServiceProvider)
      .getPoints(
        pageNum: page,
        pageSize: 20,
        cancelToken: ct,
      );
});

class _PointRecord extends ConsumerWidget {
  const _PointRecord();

  Future<void> _refreshPoints(WidgetRef ref) async {
    ref.invalidate(_listProvider);
    await ref.read(_listProvider(1).future);
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final totalResult = ref.watch(_listProvider(1));
    final Widget child;
    if (totalResult.valueOrNull?.total == 0) {
      child = RefreshableEmptyView(
        onTap: () => _refreshPoints(ref),
        message: 'No point records yet',
      );
    } else if (totalResult.hasError && !totalResult.isLoading) {
      final e = totalResult.error;
      child = RefreshableEmptyView(
        onTap: () => _refreshPoints(ref),
        message: isNetworkError(e) ? context.l10nME.networkError : '$e\n${context.l10nME.clickToRetryButton}',
      );
    } else {
      child = ListView.builder(
        padding: const EdgeInsets.symmetric(vertical: 12.0),
        itemCount: totalResult.valueOrNull?.total ?? 4,
        itemBuilder: (context, index) {
          final page = index ~/ 20 + 1;
          final indexInPage = index % 20;
          final result = ref.watch(_listProvider(page));
          final lastInLastPage = ref.watch(_listProvider(page - 1)).valueOrNull?.list.lastOrNull;
          return result.maybeWhen(
            data: (data) {
              if (indexInPage >= data.list.length) {
                return null;
              }
              final item = data.list[indexInPage];
              final last = indexInPage > 0 ? data.list[indexInPage - 1] : lastInLastPage;
              final showDate =
                  last == null ||
                  last.createTime.withDateTimeFormat(format: 'MMM dd, yyyy') !=
                      item.createTime.withDateTimeFormat(format: 'MMM dd, yyyy');
              return ProviderScope(
                overrides: [_itemProvider.overrideWithValue((item, showDate))],
                child: const _PointItem(),
              );
            },
            orElse: () => const _PointItemShimmer(),
          );
        },
      );
    }
    return AppScaffold(
      appBarBackgroundColor: context.theme.cardColor,
      appBarActions: [
        IconButton(
          icon: const Icon(Icons.help_outline, color: Color(0XFF9c9ca4), size: 30),
          onPressed: () {
            showModalBottomSheet(
              context: context,
              backgroundColor: ColorName.cardColorDark,
              clipBehavior: Clip.antiAlias,
              isScrollControlled: true,
              builder: (context) => Theme(
                data: themeBy(
                  meTheme: defaultMEThemeDark,
                  locale: Localizations.localeOf(context),
                ),
                child: const _PointHelpDialog(),
              ),
            );
          },
        ),
      ],
      bodyPadding: EdgeInsets.zero,
      body: RefreshIndicator(
        onRefresh: () => _refreshPoints(ref),
        color: context.meTheme.notificationColor,
        child: Column(
          children: [
            const _Header(),
            Expanded(child: child),
          ],
        ),
      ),
    );
  }
}

final _itemProvider = Provider.autoDispose<(Point point, bool showDate)>(
  (ref) => throw UnimplementedError(),
);

class _PointHelpDialog extends StatelessWidget {
  const _PointHelpDialog();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 16.0,
        vertical: 24.0,
      ).copyWith(bottom: 0.0),
      child: Column(
        spacing: 24.0,
        mainAxisSize: MainAxisSize.min, // 不要占用全部空间
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Introducing Card3 Points',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                icon: const Icon(Icons.close, size: 24),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
          const Text(
            'Card3 Points is an innovative incentive program designed to reward your engagement and participation. '
            'Each Card3 user is associated with a unique points account, '
            'allowing you to track and accumulate points independently. '
            'Whether you\'re tapping through content or completing tasks, '
            'every action you take earns you valuable points. '
            'Start engaging today and watch your rewards grow with Card3 Points!',
            style: TextStyle(fontSize: 16, height: 1.5),
          ),
          const Gap.v(30.0),
        ],
      ),
    );
  }
}

class _Header extends ConsumerWidget {
  const _Header();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final localUser = ref.watch(userRepoProvider);
    final userInfoResult = ref.watch(fetchUserInfoProvider());
    final userInfo = userInfoResult.valueOrNull ?? localUser;
    return Container(
      width: double.infinity,
      color: context.theme.cardColor,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Column(
          spacing: 8.0,
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Points',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 24,
              ),
            ),
            Text(
              userInfo?.integral.toString() ?? '---',
              style: const TextStyle(
                fontSize: 48,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _PointItem extends ConsumerWidget {
  const _PointItem();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final (point, showDate) = ref.watch(_itemProvider);
    final bool isPositive = point.integral > 0;
    final Color valueColor = isPositive ? const Color(0xFFFFD700) : Colors.red;
    final String valuePrefix = isPositive ? '+' : '';

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showDate)
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
            child: Text(
              point.createTime.withDateTimeFormat(format: 'MMM dd, yyyy'),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        Container(
          margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 16),
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
          decoration: BoxDecoration(
            color: const Color(0xFF2D2D3A),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Row(
            children: [
              // 左侧：积分项目名称和时间
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      point.description,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      point.createTime.withDateTimeFormat(format: 'HH:mm'),
                      style: TextStyle(
                        color: Colors.grey[400],
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),

              // 右侧：积分值
              Text(
                '$valuePrefix${point.integral}',
                style: TextStyle(
                  color: valueColor,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class _PointItemShimmer extends StatelessWidget {
  const _PointItemShimmer();

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 16),
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
      decoration: BoxDecoration(
        color: const Color(0xFF2D2D3A),
        borderRadius: BorderRadius.circular(16),
      ),
      child: MEShimmer(
        child: Row(
          children: [
            Expanded(
              child: Column(
                spacing: 2.0,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 100,
                    height: 20,
                    color: context.theme.cardColor,
                  ),
                  const SizedBox(height: 4),
                  Container(
                    width: 80,
                    height: 16,
                    color: context.theme.cardColor,
                  ),
                ],
              ),
            ),
            Container(
              width: 60,
              height: 24,
              color: context.theme.cardColor,
            ),
          ],
        ),
      ),
    );
  }
}
