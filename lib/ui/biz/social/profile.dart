import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

@FFAutoImport()
import '/models/card.dart' show EthccProfile, Social, SocialPlatform;
import '/models/user.dart' show UserInfo, UserRelation;
import '/provider/api.dart' show apiServiceProvider;
import '/provider/card.dart';
import '/provider/user.dart' show fetchUserRelationProvider;
import '/ui/widgets/social/data.dart' show SocialSvgIcon;

@FFRoute(name: '/social/profile')
class SocialProfilePage extends StatelessWidget {
  const SocialProfilePage({
    super.key,
    required this.code,
    this.profile,
  });

  final String code;
  final UserInfo? profile;

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: themeBy(
        meTheme: defaultMEThemeDark,
        locale: Localizations.localeOf(context),
      ),
      child: _SocialProfilePage(code: code, profile: profile),
    );
  }
}

final _dataProvider = Provider.autoDispose<(String, UserInfo?)>(
  (ref) => throw UnimplementedError(),
);

class _SocialProfilePage extends ConsumerStatefulWidget {
  const _SocialProfilePage({
    required this.code,
    this.profile,
  });

  final String code;
  final UserInfo? profile;

  @override
  ConsumerState<_SocialProfilePage> createState() => _SocialProfilePageState();
}

class _SocialProfilePageState extends ConsumerState<_SocialProfilePage> {
  @override
  Widget build(BuildContext context) {
    final code = widget.profile?.referralCode ?? widget.code;
    final localUser = ref.watch(userRepoProvider);
    final profileResult = ref.watch(fetchPublicProfileProvider(code: code)).valueOrNull;
    final showRelation =
        localUser != null && profileResult != null && localUser.referralCode != profileResult.referralCode;
    return ProviderScope(
      overrides: [
        _dataProvider.overrideWithValue((code, widget.profile)),
      ],
      child: AppScaffold(
        appBarActions: [const _LoadingIndicator()],
        body: CustomScrollView(
          slivers: [
            const SliverGap.v(40.0),
            const _Header(),
            const SliverGap.v(20.0),
            Consumer(
              builder: (context, ref, _) {
                final bool? isETHCCMode = switch (widget.profile) {
                  UserInfo() => ref.watch(validateETHCCProfileProvider(validateProfile: true)).valueOrNull,
                  _ => ref.watch(validateETHCCProfileByCodeProvider(code: code)).valueOrNull,
                };
                if (isETHCCMode != true) {
                  return const SliverToBoxAdapter();
                }
                return const _ETHCCProfile();
              },
            ),
            const _SocialCardList(),
            const SliverGap.v(80.0),
          ],
        ),
        bottomButtonBuilder: showRelation ? (_) => const _Relation() : null,
      ),
    );
  }
}

class _LoadingIndicator extends ConsumerWidget {
  const _LoadingIndicator();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userCode = ref.watch(userRepoProvider)?.referralCode;
    final (code, profile) = ref.watch(_dataProvider);
    final modeResult = switch (profile) {
      UserInfo() => ref.watch(validateETHCCProfileProvider(validateProfile: true)),
      _ => ref.watch(validateETHCCProfileByCodeProvider(code: code)),
    };
    final profileResult = ref.watch(fetchPublicProfileProvider(code: code));
    final socialsResult = ref.watch(fetchSocialsProvider(code: code));
    final relationResult = ref.read(fetchUserRelationProvider(code: code));
    final loading =
        modeResult.isLoading ||
        profileResult.isLoading ||
        socialsResult.isLoading ||
        (code != userCode && relationResult.isLoading);
    if (loading) {
      return const Padding(
        padding: EdgeInsetsDirectional.only(end: 4.0),
        child: AppLoading(size: 44.0),
      );
    }
    return const SizedBox.shrink();
  }
}

class _Header extends ConsumerWidget {
  const _Header();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final (code, profile) = ref.watch(_dataProvider);
    final profileResult = ref.watch(fetchPublicProfileProvider(code: code));
    final effectiveProfile = profileResult.valueOrNull ?? profile;
    return SliverToBoxAdapter(
      child: Column(
        spacing: 6.0,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: context.theme.dividerColor, width: 2),
            ),
            child: Builder(
              builder: (context) {
                if (profile == null && profileResult.isLoading) {
                  return const AppLoading();
                }
                return UserAvatar(
                  user: effectiveProfile,
                  dimension: 120.0,
                  placeholderColor: context.themeColor,
                );
              },
            ),
          ),
          const Gap.v(12.0),
          Text(
            effectiveProfile?.run((it) => it.name.isNotEmpty ? it.name : '(Name)') ?? '',
            style: const TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          if (effectiveProfile?.title case final title? when title.isNotEmpty)
            Text(
              title,
              style: context.textTheme.bodySmall?.copyWith(fontSize: 16),
              textAlign: TextAlign.center,
            ),
          if (effectiveProfile?.company case final company? when company.isNotEmpty)
            Text(
              company,
              style: context.textTheme.bodySmall?.copyWith(fontSize: 16),
              textAlign: TextAlign.center,
            ),
        ],
      ),
    );
  }
}

Widget _buildTitle(BuildContext context, String title) {
  return Padding(
    padding: const EdgeInsets.only(top: 12.0),
    child: Text(
      title,
      style: const TextStyle(
        fontWeight: FontWeight.bold,
      ),
    ),
  );
}

class _ETHCCProfile extends ConsumerWidget {
  const _ETHCCProfile();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final (code, _) = ref.watch(_dataProvider);
    final result = ref.watch(fetchEthccProfileProvider(code: code));
    return SliverToBoxAdapter(
      child: AnimatedSize(
        duration: kThemeAnimationDuration,
        curve: Curves.easeInOutCubic,
        child: result.maybeWhen(
          data: (data) {
            if (data == null || data.isEmpty) {
              return const SizedBox.shrink();
            }
            return _buildProfile(context, data);
          },
          orElse: () => const SizedBox.shrink(),
        ),
      ),
    );
  }

  Widget _buildProfile(BuildContext context, EthccProfile profile) {
    return Column(
      spacing: 6.0,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (profile.roles.isNotEmpty) ...[
          _buildTitle(context, 'I am a/an'),
          _buildItems(context, profile.roles),
        ],
        if (profile.topics.isNotEmpty) ...[
          _buildTitle(context, 'Talk to me about'),
          _buildItems(context, profile.topics),
        ],
        if (profile.githubHandle.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 12.0),
            child: _GitHubContributions(profile.githubHandle),
          ),
      ],
    );
  }

  Widget _buildItems(
    BuildContext context,
    List<String> items, {
    void Function(String item)? onItemTap,
  }) {
    return Wrap(
      spacing: 6.0,
      runSpacing: 6.0,
      children: items
          .map(
            (item) => RippleTap(
              onTap: () => onItemTap?.call(item),
              padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
              shape: RoundedRectangleBorder(
                borderRadius: RadiusConstants.max,
                side: BorderSide(color: context.themeColor, width: 2.0),
              ),
              child: Text(
                item,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          )
          .toList(),
    );
  }
}

class _GitHubContributions extends ConsumerWidget {
  const _GitHubContributions(this.handle);

  final String handle;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final result = ref.watch(fetchGitHubContributionsProvider(handle: handle));
    final noDataWidget = Container(
      height: 129.0,
      alignment: Alignment.center,
      child: const Text(
        'No data available.',
        style: TextStyle(fontSize: 18.0, fontWeight: FontWeight.bold),
      ),
    );
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 20),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: context.theme.dividerColor),
      ),
      child: Column(
        spacing: 12.0,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Text.rich(
              TextSpan(
                children: [
                  TextSpan(
                    text: result.maybeWhen(
                      data: (data) {
                        if (data == null) {
                          return "$handle's";
                        }
                        return data.calendar.total.toString();
                      },
                      orElse: () => "$handle's",
                    ),
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const TextSpan(text: ' public contributions in the last year'),
                ],
              ),
            ),
          ),
          result.when(
            data: (data) {
              if (data == null) {
                return noDataWidget;
              }
              return GitHubContributionGraph(collection: data);
            },
            loading: () => const SizedBox(height: 129.0, child: AppLoading()),
            error: (e, s) => noDataWidget,
          ),
        ],
      ),
    );
  }
}

class _SocialItem {
  const _SocialItem({
    required this.platform,
    required this.handle,
    required this.isVerified,
  });

  final SocialPlatform platform;
  final String handle;
  final bool isVerified;

  Future<bool> launchIfApplicable() async {
    String? url;
    if (handle.trim() case final handle when handle.isNotEmpty) {
      if (platform.url.trim() case final u when u.isNotEmpty) {
        url = '$u${handle.replaceAll(u, '')}';
      } else {
        url = handle;
      }
      url = url.trim();
    }

    if (url == null || url.isEmpty) {
      Card3ToastUtil.showToast(message: ToastMessages.couldNotLaunch(url.toString()));
      return false;
    }

    final uri = Uri.tryParse(url);
    if (uri == null || uri.scheme.isEmpty || uri.host.isEmpty) {
      Card3ToastUtil.showToast(message: ToastMessages.couldNotLaunch(uri.toString()));
      return Future.value(false);
    }

    if (!await canLaunchUrl(uri)) {
      Card3ToastUtil.showToast(message: ToastMessages.couldNotLaunch(uri.toString()));
      return false;
    }

    if (platform == SocialPlatform.link) {
      final ask = await TinyDialog.show<bool>(
        text: 'You are about to open an external link, proceed with caution.',
        captionText: uri.toString(),
        buttons: (context) => [
          Row(
            spacing: 10.0,
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              TextButton(
                onPressed: () => Navigator.of(context).maybePop(false),
                child: Text(context.l10nME.laterButton),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).maybePop(true),
                child: Text(context.l10nME.openButton),
              ),
            ],
          ),
        ],
      );
      if (ask != true) {
        return false;
      }
    }

    return launchUrl(uri, mode: LaunchMode.externalApplication);
  }
}

class _SocialCardList extends ConsumerWidget {
  const _SocialCardList();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final (code, _) = ref.watch(_dataProvider);
    final socialsAsync = ref.watch(fetchSocialsProvider(code: code));
    return SliverToBoxAdapter(
      child: AnimatedSize(
        duration: kThemeAnimationDuration,
        curve: Curves.easeInOutCubic,
        child: socialsAsync.maybeWhen(
          data: (data) => _buildSocialCardsList(context, data),
          orElse: () => const SizedBox.shrink(),
        ),
      ),
    );
  }

  Widget _buildSocialCardsList(BuildContext context, List<Social> socials) {
    final socialItems = <_SocialItem>[];
    for (final social in socials) {
      final platform = SocialPlatform.fromName(social.platformName);
      if (platform != null) {
        socialItems.add(
          _SocialItem(
            platform: platform,
            handle: social.handleName,
            isVerified: social.isVerify,
          ),
        );
      }
    }
    if (socialItems.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      spacing: 16.0,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTitle(context, 'Contact me on'),
        ...socialItems.map(
          (item) => ProviderScope(
            overrides: [_socialItemProvider.overrideWithValue(item)],
            child: const _SocialCard(),
          ),
        ),
      ],
    );
  }
}

final _socialItemProvider = Provider.autoDispose<_SocialItem>(
  (ref) => throw UnimplementedError(),
);

class _SocialCard extends ConsumerWidget {
  const _SocialCard();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final item = ref.watch(_socialItemProvider);
    return RippleTap(
      onTap: () => item.launchIfApplicable(),
      padding: const EdgeInsets.all(20),
      color: context.theme.cardColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: context.theme.dividerColor),
      ),
      child: Row(
        spacing: 16.0,
        children: [
          SocialSvgIcon(
            platform: item.platform,
            width: 48.0,
            height: 48.0,
            clipOval: true,
          ),
          Expanded(
            child: Text(
              item.handle,
              style: const TextStyle(
                fontSize: 18.0,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (item.isVerified) Assets.icons.verified.svg(width: 24, height: 24),
        ],
      ),
    );
  }
}

final _relationLoadingProvider = StateProvider.autoDispose<bool>((ref) => false);

class _Relation extends ConsumerStatefulWidget {
  const _Relation();

  @override
  ConsumerState<_Relation> createState() => _RelationState();
}

class _RelationState extends ConsumerState<_Relation> {
  late UserRelation? _userRelation;

  @override
  void initState() {
    super.initState();
    final code = ref.read(_dataProvider).$1;
    final relationResult = ref.read(fetchUserRelationProvider(code: code));
    final result = relationResult.valueOrNull;
    _userRelation = result?.$1;
  }

  Future<void> _toggleFollowing(UserRelation relation) async {
    final (code, _) = ref.read(_dataProvider);
    final result = ref.read(fetchUserRelationProvider(code: code)).valueOrNull;
    final referralCode = result?.$2;
    if (referralCode == null) {
      return;
    }
    ref.read(_relationLoadingProvider.notifier).state = true;
    try {
      final newRelation = await ref
          .read(apiServiceProvider)
          .toggleUserFollow(
            referralCode: referralCode,
            follow: !relation.following,
          );
      safeSetState(() {
        _userRelation = newRelation;
      });
    } finally {
      if (mounted) {
        ref.read(_relationLoadingProvider.notifier).state = false;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final (code, _) = ref.watch(_dataProvider);
    final result = ref.watch(fetchUserRelationProvider(code: code));
    return AnimatedSize(
      duration: kThemeAnimationDuration,
      curve: Curves.easeInOutCubic,
      child: result.maybeWhen(
        skipLoadingOnRefresh: false,
        data: (data) {
          final relation = _userRelation ?? data?.$1;
          if (relation == null) {
            return const SizedBox.shrink();
          }
          return _buildButton(relation);
        },
        orElse: () => _userRelation != null ? _buildButton(_userRelation!) : const SizedBox.shrink(),
      ),
    );
  }

  Widget _buildButton(UserRelation relation) {
    return Consumer(
      builder: (context, ref, child) {
        final text = switch (relation) {
          UserRelation(following: true, followedBy: true) => 'Mutual Following',
          UserRelation(following: true, followedBy: _) => 'Following',
          UserRelation(following: false, followedBy: _) => 'Follow',
        };
        if (relation.following) {
          return ThemeTextButton.outlined(
            onPressed: () => _toggleFollowing(relation),
            child: ref.watch(_relationLoadingProvider) ? const AppLoading() : Text(text),
          );
        }
        return ThemeTextButton(
          onPressed: () => _toggleFollowing(relation),
          child: ref.watch(_relationLoadingProvider) ? const AppLoading() : Text(text),
        );
      },
    );
  }
}
