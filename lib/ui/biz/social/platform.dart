import 'package:card3/exports.dart';
import 'package:flutter/cupertino.dart' show CupertinoTextSelectionControls;
import 'package:flutter/foundation.dart' show defaultTargetPlatform;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show Clipboard;

@FFAutoImport()
import '/models/card.dart' show Social, SocialPlatform;
import '/provider/api.dart' show apiServiceProvider;
import '/provider/card.dart';
import '/ui/widgets/social/data.dart';

@FFRoute(name: '/social/platform')
class SocialPlatformPage extends ConsumerStatefulWidget {
  const SocialPlatformPage({
    super.key,
    required this.platform,
    this.action,
    this.social,
    this.currentHandle,
  });

  final SocialPlatform platform;
  final String? action;
  final Social? social;
  final String? currentHandle;

  @override
  ConsumerState<SocialPlatformPage> createState() => _SocialPlatformPageState();
}

class _SocialPlatformPageState extends ConsumerState<SocialPlatformPage> {
  final _cancelToken = CancelToken();

  final _formKey = GlobalKey<FormState>();
  late final _handleController = TextEditingController(
    text: widget.currentHandle ?? widget.social?.handleName,
  );

  bool _isDeleting = false;
  bool _isSubmitting = false;
  late final int? _socialId = widget.social?.id;
  late final String? _action = widget.action;

  @override
  void initState() {
    super.initState();
    if (widget.social?.handleName.trim() case final handle?
        when handle.isNotEmpty && _handleController.text.trim().isEmpty) {
      _handleController.text = handle;
    }
  }

  @override
  void dispose() {
    _handleController.dispose();
    _cancelToken.cancel();
    super.dispose();
  }

  // 验证表单
  String? _validateHandle(String? value) {
    if (value == null || value.isEmpty) {
      return 'Required';
    }

    if (widget.platform == SocialPlatform.email) {
      final emailRegex = RegExp(r'^[\w-.]+@([\w-]+\.)+[\w-]{2,}$');
      if (!emailRegex.hasMatch(value)) {
        return 'Please enter a valid email address';
      }
    }

    return null;
  }

  Widget _contextMenuBuilder(
    BuildContext context,
    EditableTextState editableTextState,
  ) {
    final List<ContextMenuButtonItem> buttonItems = editableTextState.contextMenuButtonItems;

    // 查找粘贴按钮并替换其行为
    final pasteButtonIndex = buttonItems.indexWhere(
      (item) => item.type == ContextMenuButtonType.paste,
    );

    if (pasteButtonIndex != -1) {
      final originalPasteButton = buttonItems[pasteButtonIndex];

      // 创建自定义粘贴按钮
      final customPasteButton = ContextMenuButtonItem(
        label: originalPasteButton.label,
        type: ContextMenuButtonType.paste,
        onPressed: () async {
          // 获取剪贴板内容
          final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
          if (clipboardData?.text != null) {
            String pasteText = clipboardData!.text!;

            // 去掉 https:// 和 http:// 前缀
            pasteText = pasteText.replaceFirst(RegExp(r'^https?://'), '');

            // 获取当前选择范围
            final selection = editableTextState.textEditingValue.selection;
            final currentText = editableTextState.textEditingValue.text;

            // 替换选中的文本或在光标位置插入
            final newText = currentText.replaceRange(
              selection.start,
              selection.end,
              pasteText,
            );

            // 更新文本和光标位置
            final newSelection = TextSelection.collapsed(
              offset: selection.start + pasteText.length,
            );

            editableTextState.updateEditingValue(
              TextEditingValue(
                text: newText,
                selection: newSelection,
              ),
            );
          }

          // 关闭上下文菜单
          ContextMenuController.removeAny();
        },
      );

      // 替换原始粘贴按钮
      buttonItems[pasteButtonIndex] = customPasteButton;
    }

    // 根据平台返回相应的上下文菜单
    return AdaptiveTextSelectionToolbar.buttonItems(
      anchors: editableTextState.contextMenuAnchors,
      buttonItems: buttonItems,
    );
  }

  // 处理表单提交
  Future<void> _handleSubmit() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      final platform = widget.platform;

      // 处理特殊字符
      String handle = _handleController.text.trim();
      if (platform.url case final url when url.isNotEmpty) {
        handle = handle.replaceAll(url, '');
      }

      if (platform == SocialPlatform.youtube || platform == SocialPlatform.telegram) {
        handle = handle.replaceAll(RegExp(r'^@'), '');
      }

      if (platform == SocialPlatform.whatsapp) {
        handle = handle.replaceAll(RegExp(r'\+'), '');
      }

      handle = handle.trim();

      // 根据操作类型执行不同API调用
      if (_action == 'ethcc_github') {
        // 处理GitHub提交
        await ref
            .read(apiServiceProvider)
            .updateEthccGithubHandle(
              githubHandle: handle,
              cancelToken: _cancelToken,
            );
        // 这里可以添加刷新profile的逻辑
        ref.invalidate(fetchEthccProfileProvider);
      } else {
        if (_socialId != null) {
          await ref
              .read(apiServiceProvider)
              .socialUpdate(
                socialId: _socialId.toString(),
                platformName: platform.name,
                handleName: handle,
                platformUrl: platform.url,
                cancelToken: _cancelToken,
              );
          ref.invalidate(fetchSocialsProvider);
        } else {
          // 添加社交媒体
          await ref
              .read(apiServiceProvider)
              .socialAdd(
                platformName: platform.name,
                handleName: handle,
                platformUrl: platform.url,
                cancelToken: _cancelToken,
              );
          ref.invalidate(fetchSocialsProvider);
        }
      }
      // 返回上一页
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      Card3ToastUtil.showToast(message: ToastMessages.failedToSubmit);
      rethrow;
    } finally {
      safeSetState(() {
        _isSubmitting = false;
      });
    }
  }

  // 清除社交链接
  Future<void> _handleDelete() async {
    setState(() {
      _isDeleting = true;
    });

    try {
      if (_action == 'ethcc_github') {
        await ref
            .read(apiServiceProvider)
            .updateEthccGithubHandle(
              githubHandle: '',
              cancelToken: _cancelToken,
            );
        // 这里可以添加刷新profile的逻辑
        ref.invalidate(fetchEthccProfileProvider);
      } else if (_socialId != null) {
        await ref
            .read(apiServiceProvider)
            .socialDelete(
              socialId: _socialId,
              cancelToken: _cancelToken,
            );

        ref.invalidate(fetchSocialsProvider);
      }

      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      Card3ToastUtil.showToast(message: ToastMessages.failedToDelete);
      rethrow;
    } finally {
      safeSetState(() {
        _isDeleting = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    return AppScaffold(
      body: Padding(
        padding: const EdgeInsets.only(top: 40),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    spacing: 16.0,
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          border: widget.platform.demoImageKey != null
                              ? Border.all(color: Colors.grey[300]!)
                              : Border.all(color: Colors.transparent),
                        ),
                        child: switch (widget.platform.demoImageKey) {
                          final key? => SocialDemoImage(name: key),
                          _ => null,
                        },
                      ),

                      // 平台信息
                      if (widget.platform case final platform)
                        Row(
                          spacing: 12.0,
                          children: [
                            // 平台图标
                            SocialSvgIcon(
                              platform: platform,
                              width: 40.0,
                              height: 40.0,
                              borderRadius: BorderRadius.circular(8.0),
                              clipOval: false,
                            ),
                            Expanded(
                              child: Text(
                                platform.alias ?? platform.name,
                                style: const TextStyle(
                                  fontSize: 28,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),

                      // 用户名输入框
                      Container(
                        decoration: BoxDecoration(
                          color: theme.cardColor,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: TextFormField(
                          controller: _handleController,
                          autofocus: widget.currentHandle?.isEmpty != false,
                          validator: _validateHandle,

                          /// Override default input action toolbar to catch paste event
                          contextMenuBuilder: _contextMenuBuilder,
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                          decoration: InputDecoration(
                            filled: true,
                            fillColor: theme.cardColor,
                            hintText: widget.platform.placeholder ?? 'Enter username',
                            border: InputBorder.none,
                            prefixIcon: switch (widget.platform.addon) {
                              final a? when a.isNotEmpty => Container(
                                padding: const EdgeInsets.only(
                                  left: 16,
                                  right: 16,
                                  top: 8,
                                ),
                                child: Text(
                                  a,
                                  style: const TextStyle(
                                    fontSize: 26,
                                    color: Colors.black26,
                                  ),
                                ),
                              ),
                              _ => null,
                            },
                            contentPadding: const EdgeInsets.symmetric(
                              vertical: 16,
                              horizontal: 16,
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderSide: const BorderSide(
                                color: Colors.deepPurpleAccent,
                                width: 1,
                              ),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: Colors.grey[300]!,
                                width: 1,
                              ),
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        ),
                      ),

                      // // 错误提示
                      // if (_formKey.currentState?.validate() == false)
                      //   Padding(
                      //     padding: const EdgeInsets.only(top: 8.0),
                      //     child: Text(
                      //       _validateHandle(_handleController.text) ?? '',
                      //       style: const TextStyle(
                      //         color: Colors.red,
                      //         fontSize: 12,
                      //       ),
                      //     ),
                      //   ),

                      // URL预览
                      if (widget.platform.url.or('') case final url)
                        Padding(
                          padding: const EdgeInsetsDirectional.only(start: 4.0),
                          child: ValueListenableBuilder(
                            valueListenable: _handleController,
                            builder: (context, value, _) => Text(
                              url.run((it) {
                                String text = value.text.trim();
                                if (url.isNotEmpty) {
                                  text = '$url${text.replaceAll(url, '')}';
                                }
                                return text;
                              }),
                              style: TextStyle(
                                color: Colors.grey[500],
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
              ValueListenableBuilder(
                valueListenable: _handleController,
                builder: (context, value, _) => ThemeTextButtonGroup(
                  showCancel: _socialId != null || (_action == 'ethcc_github' && value.text.trim().isNotEmpty),
                  fallbackToDefaultCancel: false,
                  onCancel: _isDeleting || _isSubmitting ? null : _handleDelete,
                  cancelThemeColor: context.meTheme.failingColor,
                  cancelChild: _isDeleting ? const AppLoading() : Text(context.l10nME.deleteButton),
                  fallbackToDefaultConfirm: false,
                  onConfirm: _isSubmitting || _isDeleting || !widget.platform.matched(value.text)
                      ? null
                      : _handleSubmit,
                  confirmChild: _isSubmitting ? const AppLoading() : Text(context.l10nME.submitButton),
                ),
              ),
              const Gap.v(24.0),
            ],
          ),
        ),
      ),
    );
  }
}
