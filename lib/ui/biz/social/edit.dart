import 'dart:convert';
import 'dart:io' as io show File;

import 'package:card3/exports.dart';
import 'package:card3/ui/widgets/social/profile/description.dart';
import 'package:flutter/material.dart';
import 'package:image_size_getter/image_size_getter.dart';

import '/provider/api.dart' show apiServiceProvider;
import '/provider/user.dart' show fetchUserInfoProvider, userRepoProvider;
import '/ui/widgets/social/profile/avatar_img.dart';

@FFRoute(name: '/social/edit-profile')
class SocialEditProfilePage extends ConsumerStatefulWidget {
  const SocialEditProfilePage({
    super.key,
    this.pendingAvatarUrl,
  });

  final String? pendingAvatarUrl;

  @override
  ConsumerState<SocialEditProfilePage> createState() => _SocialEditProfilePageState();
}

class _SocialEditProfilePageState extends ConsumerState<SocialEditProfilePage> {
  // late final TextEditingController nameController;
  // late final TextEditingController titleController;
  // late final TextEditingController companyController;

  final GlobalKey<AvatarImgPickerState> _pickerKey = GlobalKey<AvatarImgPickerState>();

  // bool _isLoading = false;
  AvatarPickResult? _avatarPickResult;

  @override
  void initState() {
    super.initState();
    _fetchUserInfo();
    if (widget.pendingAvatarUrl case final url?) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _handleSubmit(withAvatarOnly: true, pendingAvatarUrl: url);
      });
    }
  }

  @override
  void dispose() {
    // nameController.dispose();
    // titleController.dispose();
    // companyController.dispose();
    super.dispose();
  }

  Future<void> _fetchUserInfo() async {
    final localUser = ref.read(userRepoProvider);
    // nameController = TextEditingController(text: localUser?.name);
    // titleController = TextEditingController(text: localUser?.title);
    // companyController = TextEditingController(text: localUser?.company);
    if (localUser?.avatar case final avatar? when avatar.isNotEmpty) {
      _avatarPickResult = AvatarPickUrlResult(avatar);
    }

    final userInfo = await ref.read(fetchUserInfoProvider().future);
    safeSetState(() {
      // nameController.text = userInfo.name;
      // titleController.text = userInfo.title;
      // companyController.text = userInfo.company;
      if (userInfo.avatar.isNotEmpty) {
        _avatarPickResult = AvatarPickUrlResult(userInfo.avatar);
      }
    });
  }

  Future<void> _handleImageSelected(AvatarPickResult result) async {
    if (!mounted) {
      return;
    }
    setState(() {
      _avatarPickResult = result;
    });
    await _handleSubmit(withAvatarOnly: true);
  }

  Future<void> _handleSubmit({
    bool withAvatarOnly = false,
    String? pendingAvatarUrl,
  }) async {
    // setState(() {
    //   _isLoading = true;
    // });

    try {
      String? avatarUrl = pendingAvatarUrl;
      if (avatarUrl == null) {
        switch (_avatarPickResult) {
          case AvatarPickFileResult(:final path):
            final bytes = await io.File(path).readAsBytes();
            final imageResult = ImageSizeGetter.getSizeResult(MemoryInput(bytes));
            final mimeType = 'image/${imageResult.decoder.decoderName}';
            final encoded = base64Encode(bytes);
            final result = 'data:$mimeType;base64,$encoded';
            avatarUrl = await ref.read(apiServiceProvider).uploadAvatar(fileContent: result);
          case AvatarPickUrlResult(:final url):
            avatarUrl = url;
          case _:
            break;
        }
      }

      await ref
          .read(apiServiceProvider)
          .updateUserInfo(
            // name: withAvatarOnly ? null : nameController.text.trim(),
            // title: withAvatarOnly ? null : titleController.text.trim(),
            // company: withAvatarOnly ? null : companyController.text.trim(),
            avatar: avatarUrl,
          );

      if (mounted) {
        _avatarPickResult = avatarUrl != null ? AvatarPickUrlResult(avatarUrl) : null;
        ref.invalidate(fetchUserInfoProvider);
        Card3ToastUtil.showToast(message: ToastMessages.updated);
        if (!withAvatarOnly) {
          Navigator.pop(context);
        }
      }
    } catch (e) {
      if (mounted) {
        Card3ToastUtil.showToast(message: ToastMessages.failedToUpdate);
      }
      rethrow;
    } finally {
      // safeSetState(() {
      //   _isLoading = false;
      // });
    }
  }

  // Future<void> _handleClear() async {
  //   setState(() {
  //     _isLoading = true;
  //   });
  //
  //   try {
  //     await ref.read(apiServiceProvider).updateUserInfo(name: '', title: '', company: '', avatar: '');
  //     if (mounted) {
  //       ref.invalidate(fetchUserInfoProvider);
  //       Navigator.pop(context);
  //       Card3ToastUtil.showToast(message: ToastMessages.profileCleared);
  //     }
  //   } catch (e) {
  //     if (mounted) {
  //       Card3ToastUtil.showToast(message: ToastMessages.failedToClearProfile);
  //     }
  //     rethrow;
  //   } finally {
  //     safeSetState(() {
  //       _isLoading = false;
  //     });
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    final localUser = ref.watch(userRepoProvider);
    final userResult = ref.watch(fetchUserInfoProvider());
    final user = userResult.valueOrNull ?? localUser;
    // final inputBorder = UnderlineInputBorder(
    //   borderSide: BorderSide(
    //     color: context.theme.hintColor.withValues(alpha: 0.1),
    //   ),
    // );
    // final focusedBorder = inputBorder.copyWith(
    //   borderSide: BorderSide(color: context.themeColor),
    // );
    final theme = context.theme;
    final textTheme = theme.textTheme;
    return AppScaffold(
      title: 'Edit Profile',
      body: Column(
        spacing: 8.0,
        // padding: const EdgeInsets.symmetric(vertical: 24.0),
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Center(
            child: AvatarImgPicker(
              key: _pickerKey,
              avatar: _avatarPickResult,
              onImageSelected: _handleImageSelected,
              size: 120,
            ),
          ),
          const SizedBox(height: 24),
          Center(
            child: Tapper(
              onTap: () {
                showModalBottomSheet(
                  context: context,
                  scrollControlDisabledMaxHeightRatio: 0.8,
                  builder: (context) => const DescriptionActionSheet(),
                );
              },
              child: Column(
                spacing: 4.0,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    spacing: 4.0,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        user?.name.or('Name') ?? 'Name',
                        style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                        textAlign: TextAlign.center,
                      ),
                      CircleAvatar(
                        backgroundColor: textTheme.bodyMedium?.color,
                        radius: 12.0,
                        child: Icon(
                          Icons.edit_rounded,
                          color: theme.scaffoldBackgroundColor,
                          size: 16.0,
                        ),
                      ),
                    ],
                  ),
                  Text(
                    user?.title.or('Title') ?? 'Title',
                    style: TextStyle(
                      color: (user?.title ?? '').isEmpty ? textTheme.bodySmall?.color : null,
                      fontSize: 16,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                  Text(
                    user?.company.or('Company') ?? 'Company (Optional)',
                    style: TextStyle(
                      color: (user?.company ?? '').isEmpty ? textTheme.bodySmall?.color : null,
                      fontSize: 16,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ],
              ),
            ),
          ),
          // Center(
          //   child: IntrinsicWidth(
          //     child: TextField(
          //       controller: nameController,
          //       decoration: InputDecoration(
          //         enabledBorder: inputBorder,
          //         focusedBorder: focusedBorder,
          //         hintText: 'Name',
          //       ),
          //       style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          //       textAlign: TextAlign.center,
          //     ),
          //   ),
          // ),
          //
          // Center(
          //   child: IntrinsicWidth(
          //     child: TextField(
          //       controller: titleController,
          //       decoration: InputDecoration(
          //         enabledBorder: inputBorder,
          //         focusedBorder: focusedBorder,
          //         hintText: 'Title (Optional)',
          //       ),
          //       textAlign: TextAlign.center,
          //     ),
          //   ),
          // ),
          //
          // Center(
          //   child: IntrinsicWidth(
          //     child: TextField(
          //       controller: companyController,
          //       decoration: InputDecoration(
          //         enabledBorder: inputBorder,
          //         focusedBorder: focusedBorder,
          //         hintText: 'Company (Optional)',
          //       ),
          //       textAlign: TextAlign.center,
          //     ),
          //   ),
          // ),
        ],
      ),
      // bottomButtonBuilder: (context) => ThemeTextButton(
      //   onPressed: _isLoading ? null : _handleSubmit,
      //   child: _isLoading ? const AppLoading() : Text(context.l10nME.submitButton),
      // ),
    );
  }
}
