import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/models/business.dart';
import '/provider/chain.dart' show networkProvider, networksProvider;

class SelectNetwork extends ConsumerWidget {
  const SelectNetwork({super.key});

  void _switchNetwork(
    BuildContext context,
    WidgetRef ref,
    Network network,
  ) {
    AppLoading.run(() async {
      await ChainManager.instance.switchNetwork(network);
      Navigator.of(context).maybePop();
    });
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final network = ref.watch(networkProvider);
    final networks = ref.watch(networksProvider);
    return ScrollableBottomSheet(
      title: 'Select Network',
      sliversBuilder: (context) => [
        SliverList.builder(
          itemCount: networks.length,
          itemBuilder: (context, index) {
            final item = networks[index];
            final isSelected = item.chainIdEvm == network.chainIdEvm;
            return RippleTap(
              onTap: () => _switchNetwork(context, ref, item),
              margin: const EdgeInsets.symmetric(horizontal: 16.0).copyWith(bottom: 8),
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
              color: isSelected ? ColorName.themeColorDark.withValues(alpha: 0.1) : context.meTheme.listColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
                side: isSelected ? BorderSide(color: context.themeColor) : BorderSide.none,
              ),
              child: Row(
                children: [
                  // 网络图标
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: MEImage(
                      item.iconUrl,
                      clipOval: true,
                      fit: BoxFit.cover,
                      alternativeSVG: true,
                      emptyBuilder: (context) => const Icon(
                        Icons.account_balance_wallet,
                        size: 24,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),

                  // 网络名称和信息
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item.name,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        if (item.testnet)
                          Text(
                            'Testnet',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                      ],
                    ),
                  ),

                  // 选中标记
                  if (isSelected)
                    const Icon(
                      Icons.check_circle,
                      color: ColorName.themeColorDark,
                      size: 30,
                    ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }
}
