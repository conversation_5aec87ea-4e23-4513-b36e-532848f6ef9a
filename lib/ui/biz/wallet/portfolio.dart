import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '/models/business.dart';
import '/provider/chain.dart' show networkProvider, walletAddressProvider;
import '/provider/token.dart';
import '/services/select_token.dart';
import '/ui/biz/wallet/select_network.dart';

final _refreshKey = Provider.autoDispose<GlobalKey<RefreshIndicatorState>>(
  (ref) => GlobalKey(),
);

@FFRoute(name: '/wallet/portfolio')
class WalletPortfolioPage extends ConsumerStatefulWidget {
  const WalletPortfolioPage({super.key});

  @override
  ConsumerState<WalletPortfolioPage> createState() => _WalletState();
}

class _WalletState extends ConsumerState<WalletPortfolioPage> {
  static Future<void> _onRefresh(WidgetRef ref) async {
    final network = ref.read(networkProvider);
    final address = ref.read(walletAddressProvider);
    await writeWalletPortfolioOKXCache(network, address, null);
    await writeWalletPortfolioAstroxCache(network, address, null);
    ref.invalidate(balanceDiffProvider);
    await ref.read(fetchWalletPortfolioProvider.future);
  }

  @override
  Widget build(BuildContext context) {
    final refreshKey = ref.watch(_refreshKey);
    return AppScaffold(
      titleBuilder: (context) => DefaultTextStyle.merge(
        style: const TextStyle(fontSize: 14.0, fontWeight: FontWeight.normal),
        child: const Center(child: _NetworkSelector()),
      ),
      appBarActions: [
        RippleTap(
          onTap: () => context.navigator.pushNamed(Routes.walletManagement.name),
          padding: const EdgeInsets.all(10.0),
          shape: const CircleBorder(),
          child: Assets.icons.setting.index.svg(
            width: 28.0,
            height: 28.0,
            colorFilter: const Color(0xff4a4a4f).filter,
          ),
        ),
      ],
      body: RefreshIndicator(
        key: refreshKey,
        onRefresh: () => _onRefresh(ref),
        child: CustomScrollView(
          slivers: [
            SliverList(
              delegate: SliverChildListDelegate([
                const _WalletInfo(),
                const SizedBox(height: 20),
                const _Buttons(),
              ]),
            ),
            const SliverGap.v(20.0),
            const _TokenList(),
            const SliverGap.v(50.0),
          ],
        ),
      ),
    );
  }
}

class _NetworkSelector extends ConsumerWidget {
  const _NetworkSelector();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentNetwork = ref.watch(networkProvider);
    return GestureDetector(
      onTap: () {
        showModalBottomSheet(
          context: context,
          scrollControlDisabledMaxHeightRatio: 0.7,
          backgroundColor: Colors.white,
          clipBehavior: Clip.antiAlias,
          shape: RoundedRectangleBorder(
            borderRadius: const BorderRadius.vertical(
              top: Radius.circular(20.0),
            ),
            side: BorderSide(
              color: context.theme.dividerColor,
            ),
          ),
          builder: (context) => const SelectNetwork(),
        );
      },
      child: Container(
        constraints: const BoxConstraints(maxWidth: 120.0),
        height: 40.0,
        padding: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          color: context.theme.cardColor,
          borderRadius: BorderRadius.circular(24),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 网络图标
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
              ),
              child: MEImage(
                currentNetwork.iconUrl,
                clipOval: true,
                fit: BoxFit.cover,
                alternativeSVG: true,
                emptyBuilder: (context) => Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Colors.deepPurple[200],
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                      color: Colors.black,
                      width: 2,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 4),
            Flexible(
              child: Text(
                currentNetwork.shortName,
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const Icon(Icons.keyboard_arrow_down, size: 24),
          ],
        ),
      ),
    );
  }
}

class _WalletInfo extends ConsumerWidget {
  const _WalletInfo();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final address = ref.watch(walletAddressProvider);
    final result = ref.watch(fetchWalletPortfolioProvider);

    // 格式化钱包地址显示：前6位 + ... + 后4位
    String? displayAddress;
    if (address != null && address.isNotEmpty) {
      displayAddress = address.length > 10
          ? '${address.substring(0, 6)}...${address.substring(address.length - 4)}'
          : address;
    }

    return Container(
      margin: const EdgeInsets.only(top: 20),
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 20),
      child: Column(
        spacing: 16.0,
        mainAxisSize: MainAxisSize.min,
        children: [
          // 余额标题
          const Text(
            'Balance',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 18,
            ),
          ),

          // 余额金额
          AutoSizeText.rich(
            TextSpan(
              children: [
                if (result.valueOrNull?.summary.hasEmptyValue == true)
                  TextSpan(
                    text: '≈',
                    style: TextStyle(
                      color: context.meTheme.captionTextColor,
                      height: 1.0,
                    ),
                  ),
                const TextSpan(text: '\$ '),
                TextSpan(
                  text: result.valueOrNull?.summary.totalValueUsd.toNumerical(fractionDigits: 4) ?? '-',
                  style: TextStyle(
                    color: context.textTheme.bodyMedium?.color,
                    fontFamily: FontFamily.mMMMono,
                  ),
                ),
              ],
            ),
            style: TextStyle(
              color: context.meTheme.captionTextColor,
              fontSize: 42.0,
              fontWeight: FontWeight.bold,
              height: 1.0,
            ),
          ),

          // 钱包地址
          GestureDetector(
            onTap: () {
              if (address == null || address.isEmpty) {
                return;
              }
              // 复制钱包地址
              Clipboard.setData(ClipboardData(text: address));
              Card3ToastUtil.showToast(message: ToastMessages.copied);
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  displayAddress ?? '--',
                  style: const TextStyle(
                    color: ColorName.themeColorDark,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(width: 5),
                const Icon(
                  Icons.copy_outlined,
                  color: ColorName.themeColorDark,
                  size: 20,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _Buttons extends ConsumerWidget {
  const _Buttons();

  // 处理发送按钮点击事件
  void _handleSendButtonTap(
    BuildContext context,
    Network? currentNetwork,
    List<IToken> tokens,
  ) {
    showTokenSelection(context, tokens, (token) {
      Navigator.of(context).pushNamed(
        Routes.walletSend.name,
        arguments: Routes.walletSend.d(token: token),
      );
    });
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final network = ref.watch(networkProvider);
    final walletAddress = ref.watch(walletAddressProvider);
    final List<IToken> tokens = ref.watch(fetchWalletPortfolioProvider).valueOrNull?.tokens ?? [];

    return Row(
      spacing: 10.0,
      children: [
        _buildActionButton(
          context,
          icon: Assets.icons.receive.svg(width: 30, height: 30),
          label: 'Receive',
          onTap: () {
            if (walletAddress == null || walletAddress.isEmpty) {
              return;
            }
            // 显示接收地址二维码
            ScrollableBottomSheet.show(
              builder: (context) => const _ReceiveSheet(),
              heightFactor: 0.6,
            );
          },
        ),
        // _buildActionButton(
        //   context,
        //   icon: Assets.icons.swap.svg(width: 30, height: 30),
        //   label: 'Swap',
        //   onTap: () async {
        //     // 处理交换操作
        //   },
        // ),
        _buildActionButton(
          context,
          icon: Assets.icons.send.svg(width: 30, height: 30),
          label: 'Send',
          onTap: () => _handleSendButtonTap(context, network, tokens),
        ),
      ],
    );
  }

  String getBypassUrl(String originalUrl) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final uri = Uri.parse(originalUrl);

    // 添加随机参数绕过深度链接匹配
    return uri
        .replace(
          queryParameters: {
            ...uri.queryParameters,
            'bypass_${timestamp.toString()}': '1',
            'source': 'external_launch',
            'ref': 'direct_open',
          },
        )
        .toString();
  }

  // 单个操作按钮
  Widget _buildActionButton(
    BuildContext context, {
    required Widget icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 14),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: context.themeColor,
          ),
          child: Column(
            spacing: 5.0,
            mainAxisSize: MainAxisSize.min,
            children: [
              icon,
              Text(
                label,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _ReceiveSheet extends ConsumerWidget {
  const _ReceiveSheet();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final address = ref.watch(walletAddressProvider);
    return ScrollableBottomSheet(
      title: 'Receive',
      sliversBuilder: (context) => [
        SliverToBoxAdapter(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 40.0),
            alignment: Alignment.center,
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(color: context.theme.disabledColor),
                borderRadius: BorderRadius.circular(16.0),
                color: Colors.white,
              ),
              child: QrImageView(
                data: address ?? '',
                version: QrVersions.auto,
                size: 300.0,
                padding: const EdgeInsets.all(16.0),
              ),
            ),
          ),
        ),
        const SliverGap.v(20.0),
        SliverToBoxAdapter(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 40.0),
            alignment: Alignment.center,
            child: Container(
              width: 300.0,
              padding: const EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                borderRadius: 16.0.rCircular,
                color: context.theme.scaffoldBackgroundColor,
              ),
              child: Row(
                spacing: 2.0,
                children: [
                  Expanded(
                    child: Text(
                      address ?? '(Not available)',
                      style: const TextStyle(fontSize: 18.0),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (address != null)
                    CopyButton(
                      onCopy: () => address,
                      onCopyToast: () => ToastMessages.copied,
                      dimension: 32.0,
                    ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}

final _tokenItemProvider = Provider.autoDispose<IToken>(
  (ref) => throw UnimplementedError(),
);

class _TokenList extends ConsumerWidget {
  const _TokenList();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final result = ref.watch(fetchWalletPortfolioProvider);
    return result.when(
      data: (data) {
        if (data.tokens.isEmpty) {
          return SliverPadding(
            padding: const EdgeInsets.only(top: 48.0),
            sliver: SliverEmptyView(
              onTap: () => _WalletState._onRefresh(ref),
              message: context.l10n.textWalletListEmpty,
              fillRemaining: false,
            ),
          );
        }
        return DecoratedSliver(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: context.theme.cardColor,
          ),
          sliver: SliverList.separated(
            separatorBuilder: (_, _) => Gap.v(6.0, color: context.theme.scaffoldBackgroundColor),
            itemCount: data.tokens.length,
            itemBuilder: (_, index) {
              final item = data.tokens[index];
              return ProviderScope(
                overrides: [_tokenItemProvider.overrideWithValue(item)],
                child: const _TokenItem(),
              );
            },
          ),
        );
      },
      loading: () => DecoratedSliver(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: context.theme.cardColor,
        ),
        sliver: SliverList.separated(
          separatorBuilder: (_, _) => Gap.v(4.0, color: context.theme.scaffoldBackgroundColor),
          itemCount: 5,
          itemBuilder: (_, _) => const _TokenItemShimmer(),
        ),
      ),
      error: (e, s) => SliverPadding(
        padding: const EdgeInsets.only(top: 48.0),
        sliver: SliverEmptyView(
          onTap: () => _WalletState._onRefresh(ref),
          message: isNetworkError(e) ? context.l10nME.networkError : '$e\n${context.l10nME.clickToRetryButton}',
          fillRemaining: false,
        ),
      ),
    );
  }
}

class _TokenItem extends ConsumerWidget {
  const _TokenItem();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final token = ref.watch(_tokenItemProvider);
    final network = ref.watch(networkProvider);
    final tokenBalance = ref.watch(
      tokenBalanceStreamProvider(network: network, address: token.address),
    );
    final price = token.priceUsd;
    final valueUsd = switch (price) {
      final price? => price * (tokenBalance.valueOrNull?.realBalance ?? token.realBalance),
      _ => null,
    };
    return RippleTap(
      onTap: () => Navigator.of(context).pushNamed(
        Routes.walletSend.name,
        arguments: Routes.walletSend.d(token: token),
      ),
      padding: const EdgeInsets.all(16.0),
      child: Row(
        spacing: 12.0,
        children: [
          MEImage(
            token.logo,
            width: 40.0,
            height: 40.0,
            clipOval: true,
            fit: BoxFit.cover,
            alternativeSVG: true,
            emptyBuilder: (context) => Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.deepPurple[200],
                shape: BoxShape.circle,
              ),
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  token.symbol.split('').elementAtOrNull(0)?.toUpperCase().or('?') ?? '?',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
              ),
            ),
          ),

          // 代币名称和符号
          Expanded(
            child: Text(
              token.symbol,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // 代币数量及价值
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                tokenBalance.valueOrNull?.realBalance.toNumerical(fractionDigits: 4) ??
                    token.realBalance.toNumerical(fractionDigits: 4),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text.rich(
                TextSpan(
                  children: [
                    const TextSpan(text: r'$'),
                    TextSpan(text: valueUsd?.toNumerical(fractionDigits: 4) ?? '---'),
                  ],
                ),
                style: context.textTheme.bodySmall?.copyWith(fontSize: 14),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _TokenItemShimmer extends StatelessWidget {
  const _TokenItemShimmer();

  @override
  Widget build(BuildContext context) {
    return MEShimmer(
      child: Container(
        height: 72.0,
        padding: const EdgeInsets.all(16.0),
        child: Row(
          spacing: 12.0,
          children: [
            AspectRatio(
              aspectRatio: 1.0,
              child: Container(
                decoration: BoxDecoration(
                  color: context.themeColor,
                  shape: BoxShape.circle,
                ),
              ),
            ),
            Expanded(
              child: Row(
                spacing: 12.0,
                children: [
                  Expanded(
                    flex: 5,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Gap.h(
                          86.0,
                          height: 16.0,
                          color: context.theme.cardColor,
                        ),
                        const Gap.v(4.0),
                        Gap.h(
                          56.0,
                          height: 12.0,
                          color: context.theme.cardColor,
                        ),
                      ],
                    ),
                  ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Gap.h(
                        56.0,
                        height: 16.0,
                        color: context.theme.cardColor,
                      ),
                      const Gap.v(4.0),
                      Gap.h(
                        36.0,
                        height: 12.0,
                        color: context.theme.cardColor,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
