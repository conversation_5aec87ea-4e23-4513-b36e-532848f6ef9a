import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/models/user.dart' show ProfileMode;
import '/provider/api.dart' show apiServiceProvider;
import '/provider/business.dart';
import '/provider/user.dart' show fetchUserInfoProvider;

/// 模式设置页面
@FFRoute(name: '/setting/mode')
class ModeSettingPage extends ConsumerStatefulWidget {
  const ModeSettingPage({super.key});

  @override
  ConsumerState<ModeSettingPage> createState() => _ModeSettingPageState();
}

class _ModeSettingPageState extends ConsumerState<ModeSettingPage> {
  ProfileMode _selectedMode = ProfileMode.ETHCC; // 使用枚举类型
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    // 获取用户当前的模式设置
    final config = ref.read(configProvider);
    if (ref.read(fetchUserInfoProvider()).valueOrNull case final user?) {
      safeSetState(() {
        _selectedMode = user.profileMode == ProfileMode.EMPTY ? config.defaultMode : user.profileMode;
      });
    }
  }

  Future<void> _handleModeChange() async {
    setState(() {
      _isSubmitting = true;
    });

    try {
      // 将枚举转换为字符串传递给API
      final modeString = _selectedMode == ProfileMode.DEFAULT ? 'DEFAULT' : 'ETHCC';
      await ref.read(apiServiceProvider).updateUserInfo(profileMode: modeString);

      // 刷新用户信息
      ref.invalidate(fetchUserInfoProvider);

      if (mounted) {
        Card3ToastUtil.showToast(message: ToastMessages.modeChangedSuccessfully);
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        Card3ToastUtil.showToast(message: ToastMessages.failedToChangeMode);
      }
      rethrow;
    } finally {
      safeSetState(() {
        _isSubmitting = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      title: 'Mode Setting',
      body: ListView(
        padding: const EdgeInsets.symmetric(vertical: 24.0),
        children: [
          // Common Mode 选项
          _buildModeOption(
            value: ProfileMode.DEFAULT,
            title: 'Common Mode',
            description: 'Standard profile with basic features',
            isSelected: _selectedMode == ProfileMode.DEFAULT,
            onTap: () {
              setState(() {
                _selectedMode = ProfileMode.DEFAULT;
              });
            },
          ),

          const SizedBox(height: 20),

          // ETHCC Mode 选项
          _buildModeOption(
            value: ProfileMode.ETHCC,
            title: 'ETHCC Mode',
            description: 'Enhanced profile for ETHCC events',
            isSelected: _selectedMode == ProfileMode.ETHCC,
            onTap: () {
              setState(() {
                _selectedMode = ProfileMode.ETHCC;
              });
            },
          ),
        ],
      ),
      bottomButtonBuilder: (context) => ThemeTextButton(
        onPressed: _isSubmitting ? null : _handleModeChange,
        child: _isSubmitting
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2.0,
                ),
              )
            : const Text('Confirm'),
      ),
    );
  }

  Widget _buildModeOption({
    required ProfileMode value,
    required String title,
    required String description,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: context.theme.cardColor,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? ColorName.themeColorDark : Colors.grey[300]!,
            strokeAlign: BorderSide.strokeAlignInside,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Radio 图标
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                border: Border.all(
                  color: isSelected ? ColorName.themeColorDark : Colors.grey[400]!,
                  width: 2,
                ),
                color: isSelected ? ColorName.themeColorDark : Colors.transparent,
                shape: BoxShape.circle,
              ),
              child: isSelected
                  ? const Icon(
                      Icons.check,
                      size: 16,
                      color: Colors.white,
                    )
                  : null,
            ),

            const SizedBox(width: 16),

            // 文本内容
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: isSelected ? ColorName.themeColorDark : null,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
