import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/provider/settings.dart' show selectedIndexProvider;
import 'home/card.dart';
import 'home/fun.dart';
import 'home/settings.dart';

final drawerGlobalKeyProvider = Provider<GlobalKey<ScaffoldState>>((ref) {
  return GlobalKey<ScaffoldState>();
});

@FFRoute(name: '/home')
class HomePage extends ConsumerWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      key: ref.watch(drawerGlobalKeyProvider),
      body: const _MainBody(),
      bottomNavigationBar: const _BottomNavBar(),
    );
  }
}

class _MainBody extends ConsumerWidget {
  const _MainBody();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final index = ref.watch(selectedIndexProvider);
    final children = [
      const Home(),
      const Fun(),
      const Settings(),
      // const Wallet(),
    ];
    return IndexedStack(
      index: index,
      children: children,
    );
  }
}

class _NavItem {
  const _NavItem({
    required this.icon,
    required this.label,
    this.navColor,
    this.selectedColor,
    this.showSelectedBorder = true,
  });

  final SvgGenImage icon;
  final String label;
  final Color? navColor;
  final Color? selectedColor;
  final bool showSelectedBorder;
}

class _BottomNavBar extends ConsumerWidget {
  const _BottomNavBar();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selected = ref.watch(selectedIndexProvider);
    final navs = [
      _NavItem(
        icon: Assets.icons.navCard,
        label: context.l10n.labelNavCards,
      ),
      _NavItem(
        icon: Assets.icons.navReward,
        label: context.l10n.labelNavRewards,
        navColor: ColorName.cardColorDark,
        selectedColor: context.meTheme.notificationColor,
        showSelectedBorder: false,
      ),
      _NavItem(
        icon: Assets.icons.setting.index,
        label: context.l10n.labelNavSetting,
      ),
    ];

    final bottomPadding = MediaQuery.paddingOf(context).bottom.max(4.0);
    final showSelectedBorder = navs[selected].showSelectedBorder;
    final navSelectedColor = navs[selected].navColor ?? context.theme.cardColor;
    return Material(
      type: MaterialType.transparency,
      child: Container(
        height: 64 + bottomPadding + 8,
        padding: EdgeInsets.only(top: 4.0, bottom: bottomPadding + 4),
        decoration: BoxDecoration(
          border: showSelectedBorder ? Border(top: BorderSide(color: context.theme.dividerColor)) : null,
          color: navSelectedColor,
        ),
        child: Row(
          children: navs.mapIndexed((index, item) {
            final isSelected = index == selected;
            final color = isSelected ? item.selectedColor ?? context.themeColor : null;
            return Expanded(
              child: Tapper(
                onTap: () {
                  ref.read(selectedIndexProvider.notifier).state = index;
                },
                behavior: HitTestBehavior.opaque,
                child: Column(
                  spacing: 2.0,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    item.icon.svg(
                      width: 30,
                      height: 30,
                      colorFilter: color?.filter,
                    ),
                    Text(
                      item.label,
                      style: context.textTheme.bodySmall?.copyWith(color: color),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}
