import 'dart:async';

import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/feat/card/helper.dart' show CardHelper;
import '/feat/link/handler.dart' show ReferralLinkHandler;
import '/feat/scan/uni_qr.dart';
import '/internals/box.dart' show Boxes;
import '/models/card.dart' show CardInfoBasicActivated;
import '/provider/api.dart' show apiServiceProvider;
import '/provider/user.dart';
import '/ui/widgets/pin_code.dart';

const _pinCodeLength = 6;

@FFRoute(name: '/login/email')
class LoginWithEmail extends ConsumerStatefulWidget {
  const LoginWithEmail({super.key});

  @override
  ConsumerState<LoginWithEmail> createState() => _LoginWithEmailState();
}

class _LoginWithEmailState extends ConsumerState<LoginWithEmail> {
  // TODO: Remove this when Email login has been fully tested.
  /// Whether we should use Privy for login.
  final bool _usePrivyLogin = false;

  final _cancelToken = CancelToken();

  String get _email => _emailController.text.trim().replaceAll('＠', '@').replaceAll('。', '.');
  final _emailController = TextEditingController();
  final _codeController = TextEditingController();

  final _emailFocusNode = FocusNode();

  bool _isLoading = false;
  bool _isEmailInput = true; // 控制显示邮箱输入或验证码输入

  // 添加邮箱验证相关状态
  bool _emailHasError = false;
  String _emailErrorMessage = '';

  final _resendSeconds = 60;
  late int _resendCountdown = _resendSeconds;
  Timer? _resendTimer;

  @override
  void dispose() {
    _cancelToken.cancel();
    _emailController.dispose();
    _codeController.dispose();
    _emailFocusNode.dispose();
    _resendTimer?.cancel();
    super.dispose();
  }

  void _setResendTimer() {
    _resendTimer?.cancel();
    _resendTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!mounted) {
        timer.cancel();
        _resendTimer = null;
        return;
      }

      setState(() {
        if (_resendCountdown == 1) {
          timer.cancel();
          _resendTimer = null;
          _resendCountdown = _resendSeconds;
        } else {
          _resendCountdown--;
        }
      });
    });
    safeSetState(() {});
  }

  // 邮箱格式验证正则表达式
  static final RegExp _emailRegex = RegExp(
    r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
  );

  // 验证邮箱格式
  bool _validateEmail(String email) {
    if (email.isEmpty) {
      _emailErrorMessage = 'Email is required';
      return false;
    }
    if (!_emailRegex.hasMatch(email)) {
      _emailErrorMessage = 'Please enter a valid email address';
      return false;
    }
    _emailErrorMessage = '';
    return true;
  }

  // 邮箱输入变化时的处理
  void _onEmailChanged(String value) {
    // 输入时清除错误状态
    if (_emailHasError) {
      safeSetState(() {
        _emailHasError = false;
        _emailErrorMessage = '';
      });
    }
  }

  Future<void> _ensureAndGetEthereumWallet(PrivyUser user) async {
    if (user.embeddedEthereumWallets.isNotEmpty && user.embeddedSolanaWallets.isNotEmpty) {
      return;
    }

    try {
      if (user.embeddedEthereumWallets.isEmpty) {
        await user.createEthereumWallet();
        await user.refresh();
      }
      if (user.embeddedEthereumWallets.isEmpty) {
        throw Exception('Creating wallet (1) failed for $_email');
      }

      if (user.embeddedSolanaWallets.isEmpty) {
        await user.createSolanaWallet();
        await user.refresh();
      }
      if (user.embeddedSolanaWallets.isEmpty) {
        throw Exception('Creating wallet (2) failed for $_email');
      }
    } catch (e) {
      if (mounted) {
        Card3ToastUtil.showToast(
          message: ToastMessages.creatingWalletFailedWithDetails(e.toString()),
        );
      }
      rethrow;
    }
  }

  bool _shouldRetryLogin = false;

  Result<PrivyUser>? _savedPrivyUser;

  Future<void> _sendCodeThroughPrivy(String email) async {
    final result = await privyClient.email.sendCode(email);
    result.fold(
      onSuccess: (_) {},
      onFailure: (error) {
        throw error;
      },
    );
  }

  Future<void> _sendCodeThroughEmail(String email) {
    return ref.read(apiServiceProvider).emailSendCode(email: email);
  }

  Future<void> _sendCodeUniversal(String email) {
    if (_usePrivyLogin) {
      return _sendCodeThroughPrivy(email);
    } else {
      return _sendCodeThroughEmail(email);
    }
  }

  // 发送邮箱验证码
  Future<void> _sendEmailCode() async {
    final email = _email;

    // 验证邮箱格式
    if (!_validateEmail(email)) {
      safeSetState(() {
        _emailHasError = true;
      });
      return;
    }

    safeSetState(() {
      _isLoading = true;
      _emailHasError = false;
    });

    try {
      await _sendCodeUniversal(email);
      _setResendTimer();
      safeSetState(() {
        _isEmailInput = false; // 切换到验证码输入界面
        _shouldRetryLogin = false;
        _savedPrivyUser = null;

        Card3ToastUtil.showToast(message: ToastMessages.emailCodeResent);
        FocusManager.instance.primaryFocus?.unfocus();
      });
    } catch (e) {
      if (mounted) {
        Card3ToastUtil.showToast(message: ToastMessages.failedToSendEmailCode);
      }
      rethrow;
    } finally {
      safeSetState(() {
        _isLoading = false;
      });
    }
  }

  // 验证邮箱验证码
  Future<void> _verifyEmailCode() async {
    final code = _codeController.text;
    if (code.length < 6) {
      Card3ToastUtil.showToast(message: ToastMessages.invalidOrExpiredLoginCode);
      return;
    }

    safeSetState(() {
      _isLoading = true;
      _shouldRetryLogin = false;
    });

    try {
      final authorizationCompleter = Completer<String>();

      final referral = ref.read(userReferralFromProvider);
      try {
        if (_usePrivyLogin) {
          _savedPrivyUser ??= await privyClient.email.loginWithCode(
            code: code,
            email: _email,
          );

          if (!mounted) {
            return;
          }

          final result = _savedPrivyUser!;
          result.fold(
            onFailure: (error) {
              _isLoading = false;
              _shouldRetryLogin = false;

              _savedPrivyUser = null;

              safeSetState(() {
                Card3ToastUtil.showToast(message: ToastMessages.invalidOrExpiredLoginCode);
              });

              authorizationCompleter.completeError(error, StackTrace.current);
            },
            onSuccess: (user) async {
              try {
                await _ensureAndGetEthereumWallet(user);
                final privyJWT = user.identityToken!;
                final authorization = await ref
                    .read(apiServiceProvider)
                    .privyLogin(
                      token: privyJWT,
                      referral: referral,
                      cancelToken: _cancelToken,
                    );
                authorizationCompleter.complete(authorization);
              } catch (e, s) {
                authorizationCompleter.completeError(e, s);
              } finally {
                safeSetState(() {
                  _isLoading = false;
                });
              }
            },
          );
        } else {
          final authorization = await ref
              .read(apiServiceProvider)
              .emailLogin(
                email: _email,
                code: code,
                referral: referral,
                cancelToken: _cancelToken,
              );
          authorizationCompleter.complete(authorization);
        }
      } catch (e, s) {
        if (!authorizationCompleter.isCompleted) {
          authorizationCompleter.completeError(e, s);
        }
      }

      final authorization = await authorizationCompleter.future;
      final self = await ref
          .read(apiServiceProvider)
          .getSelfUserInfo(
            token: authorization,
            cancelToken: _cancelToken,
          );
      await Future.wait([
        ref.read(persistentTokenRepoProvider.notifier).update(authorization),
        ref.read(userRepoProvider.notifier).update(self),
        Boxes.initUser(self),
      ]);
      await ChainManager.instance.initialize();
      meNavigator.removeNamedAndPushAndRemoveUntil(
        Routes.home.name,
        predicate: (_) => false,
      );
    } catch (e) {
      final String message;
      if (e is ApiException) {
        message = '${e.message} (${e.code})';
      } else if (e is PrivyException) {
        message = e.message;
      } else {
        message = isNetworkError(e) ? context.l10nME.networkError : '$e';
      }

      safeSetState(() {
        Card3ToastUtil.showToast(message: ToastMessages.loginFailed(message));
        _shouldRetryLogin = true;
      });

      rethrow;
    } finally {
      safeSetState(() {
        _isLoading = false;
      });
    }
  }

  // 重新发送验证码
  Future<void> _resendCode() async {
    safeSetState(() {
      _isLoading = true;
    });
    try {
      await _sendCodeUniversal(_email);
      safeSetState(() {
        _codeController.clear();
        Card3ToastUtil.showToast(message: ToastMessages.emailCodeResent);
      });
    } catch (e) {
      if (mounted) {
        Card3ToastUtil.showToast(message: ToastMessages.failedToResendEmailCode);
      }
      rethrow;
    } finally {
      safeSetState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final referral = ref.watch(userReferralFromProvider);
    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, _) {
        if (didPop) {
          return;
        }
        ref.read(userReferralFromProvider.notifier).state = null;
      },
      child: AppScaffold(
        title: _isEmailInput ? 'Log In / Sign Up' : 'Enter login code',
        onBackButtonPressed: () {
          if (_isEmailInput) {
            Navigator.of(context).maybePop();
          } else {
            setState(() {
              _isEmailInput = true; // 切换到邮箱输入界面
              _codeController.clear();
              _shouldRetryLogin = false;
            });
            // 切换回邮箱输入界面时，重新聚焦到邮箱输入框
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _emailFocusNode.requestFocus();
            });
          }
        },
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 24),

            // 根据状态显示邮箱输入或验证码输入
            _isEmailInput ? _buildEmailInput() : _buildPinInput(),

            if (referral == null && _isEmailInput) _buildReferralScan(context),

            if (referral != null) _buildReferral(context, referral),

            if (!_isEmailInput && _isLoading) const AppLoading(size: 64.0),
          ],
        ),
        bottomButtonBuilder: (context) => Column(
          spacing: 16.0,
          children: [
            if (_isEmailInput)
              ThemeTextButton(
                onPressed: _isLoading || _resendTimer != null ? null : _sendEmailCode,
                child: _isLoading
                    ? const AppLoading()
                    : Text(
                        () {
                          final buffer = StringBuffer(context.l10nME.submitButton);
                          if (_resendTimer != null) {
                            buffer.write(' (${_resendCountdown}s)');
                          }
                          return buffer.toString();
                        }(),
                      ),
              )
            else if (_shouldRetryLogin)
              ThemeTextButton.outlined(
                onPressed: _verifyEmailCode,
                themeColor: context.meTheme.failingColor,
                text: context.l10nME.retryButton,
              ),
          ],
        ),
      ),
    );
  }

  // 邮箱输入界面
  Widget _buildEmailInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 邮箱输入框
        TextField(
          controller: _emailController,
          focusNode: _emailFocusNode,
          autofocus: true,
          enabled: !_isLoading,
          keyboardType: TextInputType.emailAddress,
          autocorrect: false,
          autofillHints: [AutofillHints.email],
          textInputAction: TextInputAction.done,
          onChanged: _onEmailChanged,
          onSubmitted: (_) => _sendEmailCode(),
          decoration: const InputDecoration(
            hintText: 'Your email',
            contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          ),
          style: const TextStyle(fontSize: 24),
        ),
        // 错误提示文本
        if (_emailHasError && _emailErrorMessage.isNotEmpty) ...[
          const SizedBox(height: 8),
          Text(
            _emailErrorMessage,
            style: const TextStyle(color: Colors.red, fontSize: 14),
          ),
        ],
        const SizedBox(height: 16),
      ],
    );
  }

  // 验证码输入界面
  Widget _buildPinInput() {
    return Column(
      spacing: 24.0,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 提示文本
        Text.rich(
          TextSpan(
            children: [
              const TextSpan(text: 'Please check '),
              TextSpan(
                text: _email,
                style: TextStyle(
                  color: context.themeColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const TextSpan(
                text: ' for an email from privy.io and enter your code below.',
              ),
            ],
          ),
          style: const TextStyle(fontSize: 16),
        ),
        // 验证码输入框
        PinCodeInputWidget(
          length: _pinCodeLength,
          controller: _codeController,
          enabled: !_isLoading,
          onCompleted: (value) {
            if (mounted) {
              _verifyEmailCode();
            }
          },
          onChanged: (value) {
            if (mounted) {
              _shouldRetryLogin = false;
              _savedPrivyUser = null;
            }
          },
        ),
        // 重新发送按钮
        Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                'Didn\'t get an email? ',
                style: TextStyle(
                  fontSize: 16,
                  color: Color(0xFF757575),
                ),
              ),
              GestureDetector(
                onTap: _isLoading || _resendTimer != null ? null : _resendCode,
                child: Text(
                  'Resend code',
                  style: TextStyle(
                    fontSize: 16,
                    color: context.themeColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              if (_resendTimer != null)
                Text(
                  ' (${_resendCountdown}s)',
                  style: const TextStyle(fontSize: 16, color: Colors.grey),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildReferralScan(BuildContext context) {
    return Center(
      child: RippleTap(
        onTap: () async {
          await Navigator.of(context).pushNamed(
            Routes.scan.name,
            arguments: Routes.scan.d(
              manager: ScanManager({_ReferralHandler()}),
            ),
          );
        },
        padding: const EdgeInsets.all(8.0),
        borderRadius: BorderRadius.circular(8.0),
        child: Text(
          'Referral by others? Press to scan the QR code.',
          style: context.textTheme.bodySmall?.copyWith(fontSize: 14.0),
        ),
      ),
    );
  }

  Widget _buildReferral(BuildContext context, String referral) {
    return Center(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.0),
          color: context.themeColor.withValues(alpha: 0.1),
        ),
        child: Column(
          children: [
            Row(
              spacing: 8.0,
              mainAxisSize: MainAxisSize.min,
              children: [
                Flexible(
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    child: PlaceholderText(
                      'You are being referred by',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: context.meTheme.primaryTextColor.withValues(alpha: 0.5),
                        fontSize: 16.0,
                      ),
                      matchedStyle: TextStyle(
                        color: context.themeColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                RippleTap(
                  onTap: () async {
                    final result = await TinyDialog.show(
                      text: 'Are you sure you want to remove the referral?',
                      buttons: (context) => [
                        Row(
                          spacing: 10.0,
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            TextButton(
                              onPressed: () => Navigator.of(context).maybePop(false),
                              child: Text(context.l10nME.cancelButton),
                            ),
                            ElevatedButton(
                              onPressed: () => Navigator.of(context).maybePop(true),
                              child: Text(context.l10nME.confirmButton),
                            ),
                          ],
                        ),
                      ],
                    );
                    if (result == true) {
                      ref.read(userReferralFromProvider.notifier).state = null;
                    }
                  },
                  width: 24.0,
                  height: 24.0,
                  child: const FittedBox(child: Icon(Icons.close)),
                ),
              ],
            ),
            Text(
              referral,
              style: TextStyle(
                color: context.themeColor,
                fontSize: 24.0,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

final class _ReferralHandler extends ScanHandler<String> {
  @override
  Future<ScanDat> acceptSuccess(String text) async {
    final uri = Uri.tryParse(text);
    if (uri == null) {
      return ScanDat.mismatched();
    }

    if (const ReferralLinkHandler().onLink(uri)) {
      return ScanDat.processed(uri);
    }

    if (!CardHelper.isCard3Format(text)) {
      return ScanDat.mismatched();
    }

    final params = CardHelper.extractActivateParams(text);
    if (params == null) {
      return ScanDat.mismatched();
    }

    final referralCode = await AppLoading.run(() async {
      try {
        final card = await globalContainer
            .read(apiServiceProvider)
            .getCardInfoBasic(formalizedUri: params.formalizedUri);

        if (card case final CardInfoBasicActivated card when card.referralCode.isNotEmpty) {
          return card.referralCode;
        }

        return null;
      } catch (e, s) {
        handleExceptions(error: e, stackTrace: s);
        final message = switch (e) {
          ApiException() => '${e.message} (${e.code})',
          _ => '${globalL10nME.exceptionError}\n$e',
        };
        Card3ToastUtil.showToast(message: message);
        return null;
      }
    });

    if (referralCode != null) {
      globalContainer.read(userReferralFromProvider.notifier).state = referralCode;
      return ScanDat.processed(uri);
    }

    Card3ToastUtil.showToast(message: ToastMessages.notValidReferralQrCode);
    return ScanDat.mismatched();
  }
}
