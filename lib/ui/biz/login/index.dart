import 'dart:async';

import 'package:card3/exports.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:me_misc/me_misc.dart' show meRouteObserver;
import 'package:video_player/video_player.dart';

import '/feat/link/handler.dart' show ReferralLinkHandler;
import '/feat/link/helper.dart' show AppLinkHelper;
import '/internals/box.dart' show Boxes;
import '/provider/api.dart' show apiServiceProvider;
import '/ui/widgets/app_logo.dart';

@FFRoute(name: '/login')
class Login extends ConsumerStatefulWidget {
  const Login({super.key});

  @override
  ConsumerState<Login> createState() => _LoginState();
}

class _LoginState extends ConsumerState<Login> with RouteAware {
  VideoPlayerController? _vp;
  bool _isVideoReady = false;
  bool _termsAccepted = false;

  bool _disposed = false;

  @override
  void initState() {
    super.initState();
    _initializeVideo();
    _initializeReferral();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    meRouteObserver.subscribe(this, ModalRoute.of(context)!);
  }

  Future<void> _initializeVideo() async {
    if (_disposed) {
      return;
    }

    // 先释放旧资源
    await _disposeVideo();

    try {
      // 创建新的视频控制器
      _vp = VideoPlayerController.asset(
        Assets.media.home,
        videoPlayerOptions: VideoPlayerOptions(mixWithOthers: true),
      );

      await _vp!.initialize();

      if (_disposed) {
        await _vp!.dispose();
        return;
      }

      await _vp!.setLooping(true);
      await _vp!.play();

      safeSetState(() {
        _isVideoReady = true;
      });
    } catch (e) {
      safeSetState(() {
        _isVideoReady = false;
      });
      rethrow;
    }
  }

  void _initializeReferral() {
    final link = AppLinkHelper.pendingLink;
    if (link == null) {
      return;
    }
    if (const ReferralLinkHandler().onLink(link)) {
      AppLinkHelper.pendingLink = null;
    }
  }

  Future<void> _disposeVideo() async {
    if (_vp != null) {
      await _vp!.pause();
      await _vp!.dispose();
      _vp = null;
    }
  }

  @override
  void didPushNext() {
    _vp?.pause();
  }

  @override
  void didPopNext() {
    _vp?.play();
  }

  @override
  void dispose() {
    _disposed = true;
    _disposeVideo();
    meRouteObserver.unsubscribe(this);
    super.dispose();
  }

  Future<void> _loginAsGuest() {
    return AppLoading.run(() async {
      final result = await privyGuestAuthenticate();
      final completer = Completer<void>();
      result.fold(
        onSuccess: (privyUser) async {
          final privyJWT = privyUser.identityToken!;
          try {
            final authorization = await ref.read(apiServiceProvider).privyLogin(token: privyJWT);
            final user = await ref
                .read(apiServiceProvider)
                .getSelfUserInfo(
                  token: authorization,
                );
            await Future.wait([
              ref.read(persistentTokenRepoProvider.notifier).update(authorization),
              ref.read(userRepoProvider.notifier).update(user),
              Boxes.initUser(user),
            ]);
            await ChainManager.instance.initialize();
            meNavigator.pushNamedAndRemoveUntil(Routes.home.name, (_) => false);
            completer.complete();
          } catch (e, s) {
            await privyClient.logout().catchError((e, s) {
              handleExceptions(error: e, stackTrace: s);
            });
            Card3ToastUtil.showToast(
              message: isNetworkError(e) ? context.l10nME.networkError : '$e',
            );
            completer.completeError(e, s);
          }
        },
        onFailure: (error) {
          if (mounted && !_disposed) {
            Card3ToastUtil.showToast(message: error.message);
          }
          completer.completeError(error);
        },
      );
      return completer.future;
    });
  }

  @override
  Widget build(BuildContext context) {
    // 检测是否显示两个按钮（登录按钮 + 游客登录按钮）
    final showTwoButtons = isAuditing || kDebugMode;
    // 根据按钮数量调整按钮高度
    final buttonHeight = showTwoButtons ? 48.0 : 56.0;

    return Scaffold(
      body: SafeArea(
        top: false,
        child: Column(
          children: [
            // CARD3 像素文字标题 - 紧贴顶部
            Container(
              width: double.infinity,
              padding: EdgeInsets.only(
                top: MediaQuery.paddingOf(context).top + 30,
                left: 30,
                right: 30,
              ),
              child: const AppLogo(color: Color(0xff5d5e6c)),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  spacing: 16.0,
                  children: [
                    Expanded(
                      child: AnimatedSwitcher(
                        duration: kThemeAnimationDuration,
                        switchInCurve: Curves.easeInOutCubic,
                        switchOutCurve: Curves.easeInOutCubic,
                        child: _isVideoReady && _vp != null && _vp!.value.isInitialized
                            ? AspectRatio(
                                aspectRatio: _vp!.value.aspectRatio,
                                child: VideoPlayer(_vp!),
                              )
                            : const SizedBox.expand(),
                      ),
                    ),
                    Column(
                      children: [
                        _buildButton(
                          text: 'Log in / Sign up',
                          color: const Color(0xFF8560FA),
                          height: buttonHeight,
                          onTap: () {
                            if (!_termsAccepted) {
                              Card3ToastUtil.showToast(
                                message: ToastMessages.loginTermsRequired,
                              );
                              return;
                            }
                            meNavigator.pushNamed(
                              Routes.loginEmail.name,
                            );
                          },
                        ),
                        if (showTwoButtons)
                          _buildButton(
                            text: 'Guest Login',
                            color: const Color(0xFF999999),
                            height: buttonHeight,
                            onTap: () {
                              if (!_termsAccepted) {
                                Card3ToastUtil.showToast(
                                  message: ToastMessages.loginTermsRequired,
                                );
                                return;
                              }
                              _loginAsGuest();
                            },
                          ),
                      ],
                    ),
                    _buildTermsText(),
                  ],
                ),
              ),
            ),
            // 底部文字 - 使用固定的边距
            const Padding(
              padding: EdgeInsets.only(bottom: 8.0, top: 8.0),
              child: Text(
                'Powered by Card3',
                style: TextStyle(
                  color: Color(0xFFBBBBBB),
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 提取按钮创建为方法，减少重复代码
  Widget _buildButton({
    required String text,
    required Color color,
    required VoidCallback onTap,
    double height = 56.0,
  }) {
    return Container(
      width: double.infinity,
      height: height,
      margin: const EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: onTap,
          child: Center(
            child: Text(
              text,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 服务条款文本
  Widget _buildTermsText() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 勾选框
        SizedBox(
          width: 24,
          height: 24,
          child: Checkbox(
            value: _termsAccepted,
            onChanged: (value) {
              setState(() {
                _termsAccepted = value ?? false;
              });
            },
            activeColor: const Color(0xFF8560FA),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ),
        const SizedBox(width: 8),

        // 条款文本
        Flexible(
          child: Text.rich(
            TextSpan(
              children: [
                const TextSpan(text: 'I have read and agreed to '),
                TextSpan(
                  text: 'Terms of Use',
                  style: TextStyle(
                    color: context.themeColor,
                    fontWeight: FontWeight.w700,
                    decoration: TextDecoration.underline,
                  ),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      launchUrlString(envUrlEULA);
                    },
                ),
                const TextSpan(text: ' and '),
                TextSpan(
                  text: 'Privacy Policy',
                  style: TextStyle(
                    color: context.themeColor,
                    fontWeight: FontWeight.w700,
                    decoration: TextDecoration.underline,
                  ),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      launchUrlString(envUrlPP);
                    },
                ),
              ],
              style: context.textTheme.bodySmall,
            ),
          ),
        ),
      ],
    );
  }
}
