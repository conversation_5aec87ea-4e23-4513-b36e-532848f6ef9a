import 'dart:convert';
import 'dart:io' as io show File;

import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_size_getter/image_size_getter.dart';

import '/models/card.dart';
@FFAutoImport()
import '/models/user.dart' show UserInfo;
import '/provider/api.dart';
import '/provider/user.dart' show fetchUserInfoProvider, userRepoProvider;
import '/ui/widgets/social/profile/avatar_img.dart';
import 'metal.dart';
import 'normal.dart';

enum CustomizeViewType { start, main, confirm, pay, submitted }

/// 卡片定制页面
@FFRoute(name: '/customize')
class CustomizePage extends ConsumerStatefulWidget {
  const CustomizePage({
    super.key,
    this.code,
  });

  final String? code;

  @override
  ConsumerState<CustomizePage> createState() => _CustomizePageState();
}

class _CustomizePageState extends ConsumerState<CustomizePage> {
  // 表单控制器
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _titleController = TextEditingController();
  final _companyController = TextEditingController();

  // 状态变量
  CustomizeViewType _viewType = CustomizeViewType.main;
  bool _isLoading = false;
  bool _pageLoading = true;
  bool _isMetalType = false;

  AvatarPickResult? _avatarPickResult;

  CoverInfo? _coverInfo;
  CreateCardCoverResponse? _createCardCoverResponse;

  // AvatarImgPicker组件的Key (仅用于normal类型)
  final GlobalKey<AvatarImgPickerState> _avatarPickerKey = GlobalKey<AvatarImgPickerState>();

  @override
  void initState() {
    super.initState();
    _viewType = CustomizeViewType.main;
    _initializeData();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _titleController.dispose();
    _companyController.dispose();
    super.dispose();
  }

  Future<void> _initializeData() async {
    setState(() {
      _pageLoading = true;
    });

    try {
      // 并行初始化用户数据和封面信息
      await Future.wait([
        _initializeUserData(),
        _initializeCoverInfo(),
      ]);
    } finally {
      safeSetState(() {
        _pageLoading = false;
      });
    }
  }

  Future<void> _initializeUserData() async {
    UserInfo? userInfo;
    try {
      userInfo = await ref.read(fetchUserInfoProvider().future);
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      userInfo ??= ref.read(userRepoProvider);
    }

    if (userInfo == null) {
      if (mounted) {
        Navigator.of(context).pop();
      }
      return;
    }

    _nameController.text = userInfo.name;
    _titleController.text = userInfo.title;
    _companyController.text = userInfo.company;
    // 只有非Metal类型才需要头像
    if (userInfo.avatar.isNotEmpty && !_isMetalType) {
      _avatarPickResult = AvatarPickUrlResult(userInfo.avatar);
    }
  }

  Future<void> _initializeCoverInfo() async {
    final code = widget.code;
    if (code == null || code.isEmpty) {
      return;
    }
    try {
      final coverInfo = await ref.read(apiServiceProvider).getCoverInfo(code: code);
      if (coverInfo.price != 0) {
        safeSetState(() {
          _viewType = CustomizeViewType.start;
        });
      }
      safeSetState(() {
        _coverInfo = coverInfo;
        _isMetalType = coverInfo.printType == PrintType.METAL;
      });
    } catch (e) {
      if (mounted) {
        Card3ToastUtil.showToast(message: ToastMessages.failedToGetCoverInfo(code: code));
        Navigator.of(context).pop();
      }
      rethrow;
    }
  }

  void _handleImageSelected(AvatarPickResult result) {
    setState(() {
      _avatarPickResult = result;
    });
  }

  String? _validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Name is required';
    }
    if (value.length > 30) {
      return 'Name length should be less than 30';
    }
    return null;
  }

  String? _validateTitle(String? value) {
    if (value != null && value.length > 30) {
      return 'Title length should be less than 30';
    }
    return null;
  }

  String? _validateCompany(String? value) {
    if (value != null && value.length > 30) {
      return 'Company length should be less than 30';
    }
    return null;
  }

  double _calculateFontSize(String text) {
    if (text.isEmpty) {
      return 16.0;
    }
    if (text.length <= 10) {
      return 16.0;
    }
    if (text.length <= 15) {
      return 14.0;
    }
    if (text.length <= 20) {
      return 14.0;
    }
    return 12.0;
  }

  Future<void> _handleNext() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // 只有非Metal类型才需要检查图片
    if (!_isMetalType && _avatarPickResult == null) {
      Card3ToastUtil.showToast(message: ToastMessages.pleaseUploadYourPicture);
      return;
    }

    setState(() {
      _viewType = CustomizeViewType.confirm;
    });
  }

  // 添加方法来处理图片转base64
  Future<String?> _processImageToBase64() async {
    String? mimeType;
    final Uint8List imageBytes;
    switch (_avatarPickResult) {
      case null:
        return null;
      case AvatarPickFileResult(:final path):
        imageBytes = await io.File(path).readAsBytes();
      case AvatarPickUrlResult(:final url):
        final response = await ref.read(httpProvider).get(url, options: Options(responseType: ResponseType.bytes));
        imageBytes = response.data;
        final contentType = response.headers.value(Headers.contentTypeHeader);
        if (contentType != null) {
          mimeType = DioMediaType.parse(contentType).mimeType;
        }
    }

    if (mimeType == null) {
      final imageResult = ImageSizeGetter.getSizeResult(MemoryInput(imageBytes));
      mimeType = 'image/${imageResult.decoder.decoderName}';
    }

    final base64String = base64Encode(imageBytes);
    return 'data:$mimeType;base64,$base64String';
  }

  Future<void> _handleConfirm() async {
    setState(() {
      _isLoading = true;
    });

    try {
      String? fileContent;

      // 如果不是Metal类型，需要处理图片
      if (!_isMetalType) {
        fileContent = await _processImageToBase64();
        if (fileContent == null) {
          Card3ToastUtil.showToast(message: ToastMessages.failedToProcessImage);
          return;
        }
      }

      final res = await ref
          .read(apiServiceProvider)
          .createCardCover(
            code: widget.code ?? '',
            fileContent: fileContent ?? '',
            username: _nameController.text.trim(),
            title: _titleController.text.trim(),
            company: _companyController.text.trim(),
          );
      safeSetState(() {
        _createCardCoverResponse = res;
      });

      if (res.paymentLink.isNotEmpty && _coverInfo?.price != 0) {
        safeSetState(() {
          _viewType = CustomizeViewType.pay;
        });
      } else {
        safeSetState(() {
          _viewType = CustomizeViewType.submitted;
        });
      }
    } catch (e) {
      Card3ToastUtil.showToast(message: ToastMessages.failedToCreateCardCover);
      rethrow;
    } finally {
      safeSetState(() {
        _isLoading = false;
      });
    }
  }

  void _handleBack() {
    if (_viewType == CustomizeViewType.main) {
      meNavigator.pop();
    } else if (_viewType == CustomizeViewType.pay) {
      setState(() {
        _viewType = CustomizeViewType.confirm;
      });
    } else {
      setState(() {
        _viewType = CustomizeViewType.main;
      });
    }
  }

  Map<String, dynamic>? _parseThirdPartyLink() {
    if (_coverInfo?.thirdPartyLink.isEmpty ?? true) {
      return null;
    }

    return jsonDecode(_coverInfo!.thirdPartyLink) as Map<String, dynamic>;
  }

  // 解析价格单位信息 (格式: "€/30/EURO")
  CustomizeCardPriceUnit? _parsePriceUnit(String? value) {
    return const CustomizeCardPriceUnitConverter().fromJson(value);
  }

  // 显示支付完成底部弹窗
  void _showPaymentCompleteBottomSheet() {
    final link = _createCardCoverResponse?.paymentLink.trim() ?? '';
    if (link.isEmpty) {
      return;
    }
    launchUrlString(link);
    showModalBottomSheet(
      context: context,
      scrollControlDisabledMaxHeightRatio: 0.4,
      builder: (context) => Padding(
        padding: const EdgeInsets.all(24.0).copyWith(
          bottom: context.bottomPadding.max(24.0),
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Complete Payment',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: const Icon(
                    Icons.close,
                    color: Colors.black54,
                    size: 24,
                  ),
                ),
              ],
            ),
            Expanded(
              child: Column(
                children: [
                  const SizedBox(height: 16.0),
                  Row(
                    spacing: 12.0,
                    children: [
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                            color: const Color(0xFFF5F5F5),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: const Color(0xFFE0E0E0)),
                          ),
                          child: Text(
                            _createCardCoverResponse?.paymentLink ?? '',
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.black87,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                      GestureDetector(
                        onTap: () async {
                          final paymentLink = _createCardCoverResponse?.paymentLink ?? '';
                          if (paymentLink.isNotEmpty) {
                            await Clipboard.setData(ClipboardData(text: paymentLink));
                            Card3ToastUtil.showToast(message: ToastMessages.copied);
                          }
                        },
                        child: Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color: ColorName.themeColorDark,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.copy,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16.0),
                  const Text(
                    'Please copy and paste this link into your browser to complete the payment.',
                    style: TextStyle(fontSize: 14, color: Colors.black87),
                  ),
                ],
              ),
            ),

            ThemeTextButton(
              onPressed: () {
                Navigator.pop(context);
                setState(() {
                  _viewType = CustomizeViewType.submitted;
                });
              },
              text: 'I\'ve completed the payment',
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_pageLoading) {
      return const AppScaffold(
        body: AppLoading(),
      );
    }

    switch (_viewType) {
      case CustomizeViewType.start:
        return _buildStartView();
      case CustomizeViewType.confirm:
        return _buildConfirmViewWrapper();
      case CustomizeViewType.pay:
        return _buildPayView(context);
      case CustomizeViewType.submitted:
        return _buildSubmittedView();
      default:
        return _buildMainViewWrapper();
    }
  }

  Widget _buildMainViewWrapper() {
    if (_isMetalType) {
      return MetalCustomizeView(
        formKey: _formKey,
        nameController: _nameController,
        titleController: _titleController,
        companyController: _companyController,
        coverInfo: _coverInfo,
        isLoading: _isLoading,
        onNext: _handleNext,
        onConfirm: _handleConfirm,
        onBack: _handleBack,
        validateName: _validateName,
        validateTitle: _validateTitle,
        validateCompany: _validateCompany,
        calculateFontSize: _calculateFontSize,
        isConfirmView: false,
      );
    } else {
      return NormalCustomizeView(
        formKey: _formKey,
        nameController: _nameController,
        titleController: _titleController,
        companyController: _companyController,
        avatarPickResult: _avatarPickResult,
        coverInfo: _coverInfo,
        isLoading: _isLoading,
        avatarPickerKey: _avatarPickerKey,
        onImageSelected: _handleImageSelected,
        onNext: _handleNext,
        onConfirm: _handleConfirm,
        onBack: _handleBack,
        validateName: _validateName,
        validateTitle: _validateTitle,
        validateCompany: _validateCompany,
        calculateFontSize: _calculateFontSize,
        isConfirmView: false,
      );
    }
  }

  Widget _buildConfirmViewWrapper() {
    if (_isMetalType) {
      return MetalCustomizeView(
        formKey: _formKey,
        nameController: _nameController,
        titleController: _titleController,
        companyController: _companyController,
        coverInfo: _coverInfo,
        isLoading: _isLoading,
        onNext: _handleNext,
        onConfirm: _handleConfirm,
        onBack: _handleBack,
        validateName: _validateName,
        validateTitle: _validateTitle,
        validateCompany: _validateCompany,
        calculateFontSize: _calculateFontSize,
        isConfirmView: true,
      );
    } else {
      return NormalCustomizeView(
        formKey: _formKey,
        nameController: _nameController,
        titleController: _titleController,
        companyController: _companyController,
        avatarPickResult: _avatarPickResult,
        coverInfo: _coverInfo,
        isLoading: _isLoading,
        avatarPickerKey: _avatarPickerKey,
        onImageSelected: _handleImageSelected,
        onNext: _handleNext,
        onConfirm: _handleConfirm,
        onBack: _handleBack,
        validateName: _validateName,
        validateTitle: _validateTitle,
        validateCompany: _validateCompany,
        calculateFontSize: _calculateFontSize,
        isConfirmView: true,
      );
    }
  }

  Widget _buildStartView() {
    return Stack(
      children: [
        // 背景图片
        Positioned.fill(
          top: -300,
          child: Transform.rotate(
            angle: 0.2,
            child: Assets.icons.images.banner2.image(
              fit: BoxFit.contain,
            ),
          ),
        ),
        // 深色遮罩
        Positioned.fill(
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black.withValues(alpha: 0.4),
                  Colors.black.withValues(alpha: 0.7),
                ],
              ),
            ),
          ),
        ),
        AppScaffold(
          backgroundColor: Colors.transparent,
          body: SizedBox.expand(
            child: Column(
              spacing: 16.0,
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text.rich(
                  TextSpan(
                    children: [
                      const TextSpan(text: 'Customize\nyour '),
                      TextSpan(
                        text: 'Card3',
                        style: TextStyle(color: context.themeColor),
                      ),
                    ],
                  ),
                  style: const TextStyle(
                    fontSize: 36,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    height: 1.2,
                  ),
                ),
                const Text(
                  'with NFT PFPs, portraits & any images!',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                if (_coverInfo?.priceDescription case final desc? when desc.isNotEmpty)
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.only(top: 30.0),
                    child: Text(
                      desc,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
              ],
            ),
          ),
          bottomButtonBuilder: (context) => ThemeTextButton(
            onPressed: () {
              setState(() {
                _viewType = CustomizeViewType.main;
              });
            },
            text: 'Customize Now',
          ),
        ),
      ],
    );
  }

  Widget _buildSubmittedView() {
    return AppScaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 显示相应类型的卡片预览
            const Icon(
              Icons.check_circle,
              color: Color(0xFF3EDD40),
              size: 100,
            ),
            const SizedBox(height: 32),
            const Text(
              'Submitted',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 48),
            const Text(
              'Your printing code:',
              style: TextStyle(fontSize: 24),
            ),
            const SizedBox(height: 16),
            Text(
              _createCardCoverResponse?.code ?? '',
              style: TextStyle(
                color: context.themeColor,
                fontSize: 50,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
      bottomButtonBuilder: (context) => Column(
        spacing: 16.0,
        children: [
          FittedBox(
            fit: BoxFit.cover,
            child: Text(
              'You may take a screenshot before pressing the button.',
              style: context.textTheme.bodySmall?.copyWith(fontSize: 16.0),
            ),
          ),
          ThemeTextButton(
            onPressed: () => Navigator.of(context).maybePop(),
            text: context.l10nME.doneButton,
          ),
        ],
      ),
    );
  }

  Widget _buildPayView(BuildContext context) {
    // 解析thirdPartyLink获取价格和运费信息
    final thirdPartyData = _parseThirdPartyLink();
    final subTotal = _parsePriceUnit(thirdPartyData?['priceUnit']);
    final shipping = _parsePriceUnit(thirdPartyData?['shippingUnit']);

    // 计算总价
    final subtotalAmount = Decimal.tryParse(subTotal?.amount ?? '0') ?? Decimal.zero;
    final shippingAmount = Decimal.tryParse(shipping?.amount ?? '0') ?? Decimal.zero;
    final totalAmount = subtotalAmount + shippingAmount;

    return AppScaffold(
      body: ListView(
        children: [
          // 卡片预览区域
          SizedBox(
            height: 220,
            child: Center(
              child: _isMetalType ? _buildMetalCardPreview() : _buildNormalCardPreview(),
            ),
          ),

          const SizedBox(height: 16.0),

          // 账单信息
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: context.theme.scaffoldBackgroundColor.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
              color: context.theme.cardColor,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Bill',
                  style: TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 24),

                // Subtotal
                _buildBillRow(
                  'Subtotal',
                  [
                    subTotal?.symbol,
                    subTotal?.amount,
                    subTotal?.currency,
                  ].join(' '),
                ),
                const SizedBox(height: 16),

                // Shipping Fee
                _buildBillRow(
                  'Shipping Fee',
                  [
                    shipping?.symbol,
                    shipping?.amount,
                    shipping?.currency,
                  ].join(' '),
                ),
                const SizedBox(height: 24),

                // 分割线
                const Divider(height: 1.0),
                const SizedBox(height: 24),

                // Total
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Total',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${subTotal?.symbol} $totalAmount ${subTotal?.currency}',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
      bottomButtonBuilder: (context) => ThemeTextButton(
        onPressed: _showPaymentCompleteBottomSheet,
        text: 'Proceed to checkout',
      ),
    );
  }

  Widget _buildBillRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 18,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildMetalCardPreview() {
    return Center(
      child: SizedBox(
        width: 280.0,
        height: 180.0,
        child: AspectRatio(aspectRatio: 318 / 200, child: _buildMetalCardPreviewContent()),
      ),
    );
  }

  Widget _buildMetalCardPreviewContent() {
    return Container(
      width: 280,
      height: 180,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Stack(
        children: [
          // 使用 metalFrontcover 作为背景
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12.0),
              child: Assets.icons.images.metalFrontcover.svg(
                fit: BoxFit.contain,
              ),
            ),
          ),
          // 文字内容覆盖在背景上
          Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ValueListenableBuilder(
                  valueListenable: _nameController,
                  builder: (context, value, _) => Text(
                    value.text.isEmpty ? 'Your name' : value.text,
                    style: TextStyle(
                      fontSize: _calculateFontSize(value.text) * 1.3,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
                ValueListenableBuilder(
                  valueListenable: _titleController,
                  builder: (context, value, _) {
                    if (value.text.isEmpty) {
                      return const SizedBox.shrink();
                    }
                    return Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Text(
                        value.text,
                        style: const TextStyle(
                          color: Colors.grey,
                          fontSize: 14,
                        ),
                      ),
                    );
                  },
                ),
                ValueListenableBuilder(
                  valueListenable: _companyController,
                  builder: (context, value, _) {
                    if (value.text.isEmpty) {
                      return const SizedBox.shrink();
                    }
                    return Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Text(
                        value.text,
                        style: const TextStyle(
                          color: Colors.grey,
                          fontSize: 14,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNormalCardPreview() {
    return Container(
      width: 140,
      height: 220,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
        color: Colors.white,
      ),
      child: Stack(
        children: [
          // 使用 normalFrontcover 作为背景
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Assets.icons.images.normalFrontcover.image(
                fit: BoxFit.cover,
              ),
            ),
          ),
          // 头像区域
          Column(
            children: [
              Container(
                width: 140,
                height: 140,
                decoration: const BoxDecoration(
                  color: Colors.transparent,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                  child: switch (_avatarPickResult) {
                    AvatarPickUrlResult(:final url) => MEImage(
                      url,
                      fit: BoxFit.cover,
                      clipOval: false,
                      alternativeSVG: true,
                    ),
                    _ => const Icon(
                      Icons.add,
                      size: 56,
                      color: ColorName.themeColorDark,
                    ),
                  },
                ),
              ),
              // 文字内容
              Expanded(
                child: Container(
                  width: double.infinity,
                  decoration: const BoxDecoration(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(12),
                      bottomRight: Radius.circular(12),
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(8),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ValueListenableBuilder(
                          valueListenable: _nameController,
                          builder: (context, value, _) => value.text.trim().run(
                            (it) => Text(
                              it.or('Your name'),
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: _calculateFontSize(it),
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                        ValueListenableBuilder(
                          valueListenable: _titleController,
                          builder: (context, value, _) {
                            final text = value.text.trim();
                            if (text.isEmpty) {
                              return const SizedBox.shrink();
                            }
                            return Padding(
                              padding: const EdgeInsets.only(top: 2.0),
                              child: Text(
                                text,
                                style: const TextStyle(
                                  color: Colors.black45,
                                  fontSize: 10,
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            );
                          },
                        ),
                        ValueListenableBuilder(
                          valueListenable: _companyController,
                          builder: (context, value, _) {
                            final text = value.text.trim();
                            if (text.isEmpty) {
                              return const SizedBox.shrink();
                            }
                            return Padding(
                              padding: const EdgeInsets.only(top: 2.0),
                              child: Text(
                                text,
                                style: const TextStyle(
                                  color: Colors.black45,
                                  fontSize: 10,
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
