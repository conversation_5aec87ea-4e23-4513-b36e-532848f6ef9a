import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/provider/api.dart' show apiServiceProvider;
import '/provider/user.dart' show fetchUserInfoProvider, userRepoProvider;

class DescriptionActionSheet extends ConsumerStatefulWidget {
  const DescriptionActionSheet({super.key});

  @override
  ConsumerState<DescriptionActionSheet> createState() => _DescriptionState();
}

class _DescriptionState extends ConsumerState<DescriptionActionSheet> {
  late final TextEditingController nameController;
  late final TextEditingController titleController;
  late final TextEditingController companyController;

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _fetchUserInfo();
  }

  @override
  void dispose() {
    nameController.dispose();
    titleController.dispose();
    companyController.dispose();
    super.dispose();
  }

  Future<void> _fetchUserInfo() async {
    final localUser = ref.read(userRepoProvider);
    nameController = TextEditingController(text: localUser?.name);
    titleController = TextEditingController(text: localUser?.title);
    companyController = TextEditingController(text: localUser?.company);

    final userInfo = await ref.read(fetchUserInfoProvider().future);
    safeSetState(() {
      nameController.text = userInfo.name;
      titleController.text = userInfo.title;
      companyController.text = userInfo.company;
    });
  }

  Future<void> _handleSubmit() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await ref
          .read(apiServiceProvider)
          .updateUserInfo(
            name: nameController.text.trim(),
            title: titleController.text.trim(),
            company: companyController.text.trim(),
          );

      if (mounted) {
        ref.invalidate(fetchUserInfoProvider);
        Navigator.pop(context);
        Card3ToastUtil.showToast(message: ToastMessages.updated);
      }
    } catch (e) {
      if (mounted) {
        Card3ToastUtil.showToast(message: ToastMessages.failedToUpdate);
      }
      rethrow;
    } finally {
      safeSetState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _handleClear() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await ref.read(apiServiceProvider).updateUserInfo(name: '', title: '', company: '', avatar: '');
      if (mounted) {
        ref.invalidate(fetchUserInfoProvider);
        Navigator.pop(context);
        Card3ToastUtil.showToast(message: ToastMessages.profileCleared);
      }
    } catch (e) {
      if (mounted) {
        Card3ToastUtil.showToast(message: ToastMessages.failedToClearProfile);
      }
      rethrow;
    } finally {
      safeSetState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16).copyWith(
        bottom: MediaQuery.paddingOf(context).bottom.max(24.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题与关闭按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Edit Profile',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                icon: const Icon(Icons.close, color: Colors.black, size: 24),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // 输入区域
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // 姓名输入框
                  Container(
                    decoration: BoxDecoration(
                      color: context.theme.inputDecorationTheme.fillColor,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    margin: const EdgeInsets.only(bottom: 16),
                    child: TextField(
                      controller: nameController,
                      style: const TextStyle(
                        fontSize: 24,
                      ),
                      decoration: InputDecoration(
                        filled: true,
                        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                        fillColor: Colors.transparent,
                        hintText: 'Name',
                        focusedBorder: OutlineInputBorder(
                          borderSide: const BorderSide(
                            color: Colors.deepPurpleAccent,
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                            color: Colors.grey[300]!,
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        border: InputBorder.none,
                      ),
                    ),
                  ),

                  // 职位输入框
                  Container(
                    decoration: BoxDecoration(
                      color: context.theme.inputDecorationTheme.fillColor,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    margin: const EdgeInsets.only(bottom: 16),
                    child: TextField(
                      controller: titleController,
                      style: const TextStyle(
                        fontSize: 24,
                      ),
                      decoration: InputDecoration(
                        filled: true,
                        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                        fillColor: Colors.transparent,
                        hintText: 'Title (Optional)',
                        focusedBorder: OutlineInputBorder(
                          borderSide: const BorderSide(
                            color: Colors.deepPurpleAccent,
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                            color: Colors.grey[300]!,
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        border: InputBorder.none,
                      ),
                    ),
                  ),

                  // 公司输入框
                  Container(
                    decoration: BoxDecoration(
                      color: context.theme.inputDecorationTheme.fillColor,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    margin: const EdgeInsets.only(bottom: 16),
                    child: TextField(
                      controller: companyController,
                      style: const TextStyle(
                        fontSize: 24,
                      ),
                      decoration: InputDecoration(
                        filled: true,
                        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                        fillColor: Colors.transparent,
                        hintText: 'Company (Optional)',
                        focusedBorder: OutlineInputBorder(
                          borderSide: const BorderSide(
                            color: Colors.deepPurpleAccent,
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                            color: Colors.grey[300]!,
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        border: InputBorder.none,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // 底部按钮
          Row(
            children: [
              // 清除按钮
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.red.shade300),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: TextButton(
                  onPressed: _isLoading ? null : _handleClear,
                  style: TextButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                  child: Text(
                    'Clear',
                    style: TextStyle(
                      color: Colors.red.shade400,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 12),

              // 提交按钮
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: const Color(0xFF8560FA),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: TextButton(
                    onPressed: _isLoading ? null : _handleSubmit,
                    style: TextButton.styleFrom(
                      backgroundColor: const Color(0xFF8560FA),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 3,
                            ),
                          )
                        : const Text(
                            'Submit',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
