import 'dart:io' as io show File;

import 'package:card3/exports.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:image_editor/image_editor.dart' as editor;
import 'package:image_picker/image_picker.dart' as picker;
import 'package:path/path.dart' as p show extension;
import 'package:wolt_modal_sheet/wolt_modal_sheet.dart';

import '/models/business.dart' show ImageAIStyle;
import '/provider/api.dart' show apiServiceProvider;

sealed class AvatarPickResult {}

final class AvatarPickFileResult implements AvatarPickResult {
  AvatarPickFileResult(this.path);

  final String path;
}

final class AvatarPickUrlResult implements AvatarPickResult {
  AvatarPickUrlResult(this.url);

  final String url;
}

class AvatarImgPicker extends StatefulWidget {
  const AvatarImgPicker({
    super.key,
    this.avatar,
    required this.onImageSelected,
    this.size = 240,
    this.emptyBuilder,
    this.borderWidth = 1.0,
    this.backgroundColor,
    this.showEditButton = true,
  });

  /// 用于显示的头像
  final AvatarPickResult? avatar;

  /// 裁剪后的图片回调
  final FutureOr<void> Function(AvatarPickResult result) onImageSelected;

  /// 图片选择器的尺寸
  final double size;

  final WidgetBuilder? emptyBuilder;
  final double borderWidth;
  final Color? backgroundColor;
  final bool showEditButton;

  @override
  State<AvatarImgPicker> createState() => AvatarImgPickerState();
}

class AvatarImgPickerState extends State<AvatarImgPicker> {
  final _picker = picker.ImagePicker();
  final _editorKey = GlobalKey<ExtendedImageEditorState>();

  late AvatarPickResult? _avatar = widget.avatar;
  ImageAIStyle? _selectedAIStyle;
  bool _isProcessing = false;

  @override
  void didUpdateWidget(AvatarImgPicker oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.avatar != oldWidget.avatar) {
      _avatar = widget.avatar;
    }
  }

  /// 从相册选择图片 - 公开方法，允许外部调用
  Future<void> pickImage({
    bool allowLocalImage = true,
    void Function(String filePath, ImageAIStyle? aiStyle)? onFilePicked,
  }) async {
    if (_isProcessing) {
      return;
    }

    _selectedAIStyle = null;
    final pickedFile = await WoltModalSheet.show(
      context: context,
      showDragHandle: false,
      modalTypeBuilder: (_) => WoltModalType.bottomSheet(),
      modalDecorator: (child) => Theme(
        data: context.theme.apply(
          (it) => it.copyWith(
            extensions: [
              ...it.extensions.values,
              WoltModalSheetThemeData(
                backgroundColor: it.bottomSheetTheme.backgroundColor,
                topBarElevation: 0.0,
              ),
            ],
          ),
        ),
        child: child,
      ),
      pageListBuilder: (context) => [
        if (allowLocalImage) _buildUploadMethodsSheet(context),
        _buildAIStylesSheet(context, allowLocalImage: allowLocalImage),
        _buildPickImageSheet(context),
      ],
    );

    if (pickedFile is! picker.XFile || !mounted) {
      return;
    }

    try {
      if (await pickedFile.length() > 10 * 1024 * 1024) {
        Card3ToastUtil.showToast(message: 'Image size should be less than 10MB');
        return;
      }

      final croppedFilePath = await _showCropDialog(pickedFile.path);
      if (croppedFilePath == null || !mounted) {
        return;
      }

      if (onFilePicked != null) {
        onFilePicked(pickedFile.path, _selectedAIStyle);
        return;
      }

      setState(() {
        _isProcessing = true;
      });

      String? generatedUrl;
      if (_selectedAIStyle case final style?) {
        final result = await Navigator.of(context).pushNamed(
          Routes.aiGenerateImage.name,
          arguments: Routes.aiGenerateImage.d(filePath: croppedFilePath, style: style),
        );
        if (result case final String url) {
          generatedUrl = url;
        }
      }

      if (!mounted) {
        return;
      }

      final AvatarPickResult? pickResult;
      if (_selectedAIStyle != null && generatedUrl != null) {
        pickResult = AvatarPickUrlResult(generatedUrl);
      } else if (_selectedAIStyle == null) {
        pickResult = AvatarPickFileResult(croppedFilePath);
      } else {
        pickResult = null;
      }

      if (pickResult != null) {
        _avatar = pickResult;
        await widget.onImageSelected(pickResult);
      }
    } catch (e) {
      Card3ToastUtil.showToast(message: ToastMessages.failedToProcessImage);
      rethrow;
    } finally {
      safeSetState(() {
        _isProcessing = false;
      });
    }
  }

  Future<String?> _showCropDialog(String filePath) {
    return showModalBottomSheet<String>(
      context: context,
      scrollControlDisabledMaxHeightRatio: 0.85,
      enableDrag: false,
      builder: (context) => _CropDialog(
        filePath: filePath,
        editorKey: _editorKey,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final borderRadius = BorderRadius.circular(widget.size / 6);
    return GestureDetector(
      onTap: _isProcessing ? null : pickImage,
      child: Container(
        width: widget.size,
        height: widget.size,
        decoration: BoxDecoration(
          border: widget.borderWidth == 0.0 ? null : Border.all(color: Colors.grey[300]!, width: 1),
          borderRadius: borderRadius,
          color: widget.backgroundColor ?? Colors.grey[200],
        ),
        child: Stack(
          clipBehavior: Clip.none,
          fit: StackFit.expand,
          children: [
            ClipRRect(
              borderRadius: borderRadius,
              child: switch (_avatar) {
                AvatarPickFileResult(:final path) => Image.file(
                  io.File(path),
                  fit: BoxFit.cover,
                ),
                AvatarPickUrlResult(:final url) => MEImage(
                  url,
                  fit: BoxFit.cover,
                  alternativeSVG: true,
                ),
                null =>
                  widget.emptyBuilder?.call(context) ??
                      const FittedBox(fit: BoxFit.cover, child: Icon(Icons.account_circle)),
              },
            ),
            if (widget.showEditButton)
              PositionedDirectional(
                end: -4.0,
                top: -4.0,
                child: Container(
                  padding: const EdgeInsets.all(4.0),
                  decoration: BoxDecoration(
                    color: context.themeColor,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.edit,
                    color: Colors.white,
                    size: 16.0,
                  ),
                ),
              ),

            if (_isProcessing) const AppLoading(),
          ],
        ),
      ),
    );
  }

  SliverWoltModalSheetPage _buildUploadMethodsSheet(BuildContext context) {
    return WoltModalSheetPage(
      useSafeArea: false,
      topBar: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        child: Row(
          children: [
            BackButton(color: context.meTheme.primaryTextColor),
            Expanded(
              child: Text('Upload new avatar', style: context.textTheme.headlineSmall),
            ),
          ],
        ),
      ),
      hasTopBarLayer: true,
      isTopBarLayerAlwaysVisible: true,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24.0),
        child: Column(
          spacing: 12.0,
          mainAxisSize: MainAxisSize.min,
          children: [
            ThemeTextButton(
              onPressed: () => WoltModalSheet.of(context).showNext(),
              child: Builder(
                builder: (context) => Row(
                  spacing: 16.0,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Assets.icons.generate.svg(
                      width: 20,
                      height: 20,
                      colorFilter: context.iconTheme.color?.filter,
                    ),
                    const Text('Generate with AI'),
                  ],
                ),
              ),
            ),
            ThemeTextButton(
              onPressed: () => WoltModalSheet.of(context).showAtIndex(2),
              themeColor: Colors.black,
              child: Builder(
                builder: (context) => Row(
                  spacing: 16.0,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Assets.icons.upload.svg(
                      width: 20,
                      height: 20,
                      colorFilter: context.iconTheme.color?.filter,
                    ),
                    const Text('Use existing photo'),
                  ],
                ),
              ),
            ),
            Gap.v(context.bottomPadding.max(12.0)),
          ],
        ),
      ),
    );
  }

  SliverWoltModalSheetPage _buildAIStylesSheet(
    BuildContext context, {
    bool allowLocalImage = true,
  }) {
    return SliverWoltModalSheetPage(
      useSafeArea: false,
      topBar: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        child: Row(
          children: [
            BackButton(
              onPressed: allowLocalImage ? WoltModalSheet.of(context).showPrevious : Navigator.of(context).maybePop,
              color: context.meTheme.primaryTextColor,
            ),
            Expanded(
              child: Column(
                spacing: 2.0,
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Gap.v(16.0),
                  Text('Pick a style', style: context.textTheme.headlineSmall),
                  Text('Generate with AI', style: context.textTheme.bodySmall),
                ],
              ),
            ),
          ],
        ),
      ),
      hasTopBarLayer: true,
      isTopBarLayerAlwaysVisible: true,
      mainContentSliversBuilder: (context) => [
        const SliverGap.v(16.0),
        SliverPadding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          sliver: _StylesList(
            onSelected: (ImageAIStyle style) {
              _selectedAIStyle = style;
              WoltModalSheet.of(context).showNext();
            },
          ),
        ),
        const SliverGap.v(36.0),
        SliverGap.v(context.bottomPadding.max(24.0)),
      ],
    );
  }

  Future<picker.XFile?> pickImageForFilePath(picker.ImageSource source) async {
    try {
      final picked = await _picker.pickImage(
        source: source,
        imageQuality: 95,
        preferredCameraDevice: picker.CameraDevice.front,
      );
      return picked;
    } catch (e) {
      Card3ToastUtil.showToast(message: ToastMessages.failedToOpenImagePicker);
      rethrow;
    }
  }

  SliverWoltModalSheetPage _buildPickImageSheet(BuildContext context) {
    Future<void> pickImage(picker.ImageSource source) async {
      final picked = await pickImageForFilePath(source);
      if (picked != null && context.mounted) {
        Navigator.of(context).maybePop(picked);
      }
    }

    return WoltModalSheetPage(
      useSafeArea: false,
      topBar: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        child: Row(
          children: [
            BackButton(
              onPressed: () => _selectedAIStyle == null
                  ? WoltModalSheet.of(context).showAtIndex(0)
                  : WoltModalSheet.of(context).showPrevious(),
              color: context.meTheme.primaryTextColor,
            ),
            Expanded(
              child: Text(
                'Pick a photo',
                style: context.textTheme.headlineSmall,
              ),
            ),
          ],
        ),
      ),
      hasTopBarLayer: true,
      isTopBarLayerAlwaysVisible: true,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          spacing: 12.0,
          children: [
            ThemeTextButton(
              onPressed: () => pickImage(picker.ImageSource.camera),
              child: Builder(
                builder: (context) => Row(
                  spacing: 16.0,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Assets.icons.camera.svg(
                      width: 20,
                      height: 20,
                      colorFilter: context.iconTheme.color?.filter,
                    ),
                    const Text('Take photo'),
                  ],
                ),
              ),
            ),
            ThemeTextButton(
              onPressed: () => pickImage(picker.ImageSource.gallery),
              themeColor: Colors.black,
              child: Builder(
                builder: (context) => Row(
                  spacing: 16.0,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Assets.icons.scan.gallery.svg(
                      width: 20,
                      height: 20,
                      colorFilter: context.iconTheme.color?.filter,
                    ),
                    const Text('Choose from the gallery'),
                  ],
                ),
              ),
            ),
            Gap.v(context.bottomPadding.max(12.0)),
          ],
        ),
      ),
    );
  }
}

class _CropDialog extends StatelessWidget {
  const _CropDialog({
    required this.filePath,
    required this.editorKey,
  });

  final String filePath;
  final GlobalKey<ExtendedImageEditorState> editorKey;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final themeColor = context.themeColor;
    return Material(
      type: MaterialType.transparency,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
            decoration: BoxDecoration(
              borderRadius: theme.bottomSheetTheme.shape?.run((it) {
                if (it is RoundedRectangleBorder) {
                  return it.borderRadius;
                }
                return null;
              }),
              color: themeColor,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Crop Image',
                  style: TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold),
                ),
                IconButton(
                  icon: const Icon(Icons.close, color: Colors.white),
                  onPressed: () => Navigator.pop(context),
                  padding: EdgeInsets.zero,
                ),
              ],
            ),
          ),
          // 裁剪区域
          Expanded(
            child: ExtendedImage.file(
              io.File(filePath),
              fit: BoxFit.contain,
              mode: ExtendedImageMode.editor,
              extendedImageEditorKey: editorKey,
              initEditorConfigHandler: (state) {
                return EditorConfig(
                  maxScale: 8.0,
                  cropRectPadding: const EdgeInsets.all(20.0),
                  hitTestSize: 20.0,
                  cropAspectRatio: 1.0,
                );
              },
            ),
          ),
          // 底部按钮
          Padding(
            padding: const EdgeInsets.all(24.0).add(
              EdgeInsets.only(bottom: MediaQuery.paddingOf(context).bottom),
            ),
            child: ThemeTextButtonGroup(
              confirmText: 'Crop',
              onCancel: () => Navigator.maybePop(context),
              onConfirm: () {
                final rect = editorKey.currentState?.getCropRect();
                if (rect == null) {
                  return;
                }
                AppLoading.run(() async {
                  try {
                    // 简化裁剪选项
                    final option = editor.ImageEditorOption();
                    option.outputFormat = const editor.OutputFormat.jpeg(95);
                    option.addOptions([
                      editor.ClipOption(
                        x: rect.left.toInt(),
                        y: rect.top.toInt(),
                        width: rect.width.toInt(),
                        height: rect.height.toInt(),
                      ),
                      const editor.ScaleOption(650, 650),
                    ]);

                    // 执行裁剪
                    io.File? result = await editor.ImageEditor.editFileImageAndGetFile(
                      file: io.File(filePath),
                      imageEditorOption: option,
                    );
                    if (result != null) {
                      final extension = p.extension(result.path);
                      if (extension.isEmpty) {
                        result = await result.rename('${result.path}.jpg');
                      }
                      Navigator.pop(context, result.path);
                    }
                  } catch (e) {
                    Card3ToastUtil.showToast(message: ToastMessages.failedToCropImage);
                    rethrow;
                  }
                });
              },
            ),
          ),
        ],
      ),
    );
  }
}

final _stylesProvider = FutureProvider.autoDispose<List<ImageAIStyle>>((ref) async {
  final ct = ref.cancelToken();
  ref.onDispose(() {
    ct.cancel();
  });
  return ref.read(apiServiceProvider).getImageStyles(cancelToken: ct);
});

class _StylesList extends ConsumerWidget {
  const _StylesList({
    required this.onSelected,
  });

  final void Function(ImageAIStyle style) onSelected;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final result = ref.watch(_stylesProvider);
    final theme = context.theme;
    final textTheme = theme.textTheme;
    return result.when(
      skipLoadingOnRefresh: false,
      data: (data) => SliverList.separated(
        separatorBuilder: (context, index) => const Gap.v(10.0),
        itemCount: data.length,
        itemBuilder: (context, index) => RippleTap(
          onTap: () => onSelected(data[index]),
          height: 100.0,
          padding: const EdgeInsets.all(10.0),
          borderRadius: BorderRadius.circular(20.0),
          color: theme.cardColor,
          child: Row(
            spacing: 20.0,
            children: [
              AspectRatio(
                aspectRatio: 1.0,
                child: MEImage(
                  data[index].logo,
                  borderRadius: BorderRadius.circular(20.0),
                  backgroundColor: theme.dividerColor,
                ),
              ),
              Expanded(child: Text(data[index].name, style: textTheme.headlineSmall)),
            ],
          ),
        ),
      ),
      loading: () => SliverToBoxAdapter(
        child: MEShimmer(
          child: Container(
            height: 100.0,
            padding: const EdgeInsets.all(10.0),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20.0),
              color: theme.colorScheme.surface,
            ),
            child: Row(
              spacing: 20.0,
              children: [
                AspectRatio(
                  aspectRatio: 1.0,
                  child: DecoratedBox(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20.0),
                      color: theme.cardColor,
                    ),
                  ),
                ),
                const Text('Image AI Style', style: TextStyle(fontSize: 24.0)),
              ],
            ),
          ),
        ),
      ),
      error: (e, s) => SliverEmptyView(
        onTap: () => ref.invalidate(_stylesProvider),
        fillRemaining: false,
        message: isNetworkError(e) ? context.l10nME.networkError : '$e\n${context.l10nME.clickToRetryButton}',
      ),
    );
  }
}
