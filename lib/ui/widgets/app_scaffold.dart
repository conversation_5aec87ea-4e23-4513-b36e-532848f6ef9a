import 'package:flutter/material.dart';
import 'package:me_extensions/me_extensions.dart' show MENumExtension;
import 'package:me_ui/me_ui.dart' show BrightnessLayer;

import 'app_back_button.dart';
import 'app_blur_background.dart';

const _defaultBodyPadding = EdgeInsets.symmetric(horizontal: 16.0);

class AppScaffold extends StatelessWidget {
  const AppScaffold({
    super.key,
    required this.body,
    this.bodyPadding = _defaultBodyPadding,
    this.title,
    this.titleStyle,
    this.titleBuilder,
    this.automaticallyImplyLeading = true,
    this.appBarLeading,
    this.appBarPadding = const EdgeInsets.symmetric(horizontal: 6.0),
    this.appBarSpacing = 4.0,
    this.appBarActions = const <Widget>[],
    this.appBarBackgroundColor,
    this.onBackButtonPressed,
    this.showBottomButtonDivider = false,
    this.bottomButtonBuilder,
    this.backgroundColor,
    this.backgroundBuilder,
    this.defaultTextStyle,
    this.withBlurBackground = false,
  });

  final Widget body;
  final EdgeInsetsGeometry bodyPadding;
  final String? title;
  final TextStyle? titleStyle;
  final WidgetBuilder? titleBuilder;
  final bool automaticallyImplyLeading;
  final Widget? appBarLeading;
  final EdgeInsets appBarPadding;
  final double appBarSpacing;
  final List<Widget> appBarActions;
  final Color? appBarBackgroundColor;
  final VoidCallback? onBackButtonPressed;
  final bool showBottomButtonDivider;
  final WidgetBuilder? bottomButtonBuilder;
  final Color? backgroundColor;
  final WidgetBuilder? backgroundBuilder;
  final TextStyle? defaultTextStyle;
  final bool withBlurBackground;

  @override
  Widget build(BuildContext context) {
    final impliesLeading = automaticallyImplyLeading && (ModalRoute.of(context)?.impliesAppBarDismissal ?? false);
    final showAppBar =
        impliesLeading || appBarLeading != null || titleBuilder != null || title != null || appBarActions.isNotEmpty;

    final estimateBrightness = switch (backgroundColor) {
      final color? => ThemeData.estimateBrightnessForColor(color),
      _ => null,
    };
    final themeBrightness = Theme.of(context).brightness;

    Widget child = DefaultTextStyle.merge(
      style: defaultTextStyle,
      child: Column(
        children: [
          if (showAppBar)
            Material(
              type: appBarBackgroundColor == null ? MaterialType.transparency : MaterialType.canvas,
              color: appBarBackgroundColor,
              child: Padding(
                padding: appBarPadding.copyWith(
                  top: MediaQuery.paddingOf(context).top,
                ),
                child: Row(
                  spacing: appBarSpacing,
                  children: [
                    if (appBarLeading case final leading?)
                      leading
                    else if (impliesLeading)
                      AppBackButton(onPressed: onBackButtonPressed),
                    Expanded(
                      child: DefaultTextStyle.merge(
                        style: TextStyle(
                          color: switch (estimateBrightness ?? themeBrightness) {
                            Brightness.dark => Colors.white,
                            Brightness.light => null,
                          },
                          fontSize: 24.0,
                          fontWeight: FontWeight.bold,
                        ).merge(titleStyle),
                        child: switch ((titleBuilder, title)) {
                          (final builder?, _) => builder(context),
                          (_, final title?) => Text(title),
                          _ => const SizedBox.shrink(),
                        },
                      ),
                    ),
                    ...appBarActions,
                  ],
                ),
              ),
            ),
          Expanded(
            child: MediaQuery.removePadding(
              context: context,
              removeTop: showAppBar,
              child: Padding(
                padding: bodyPadding,
                child: body,
              ),
            ),
          ),
          if (bottomButtonBuilder != null) const SizedBox(height: 24.0),
          if (bottomButtonBuilder case final builder?)
            Padding(
              padding: EdgeInsets.symmetric(horizontal: bodyPadding.horizontal / 2).copyWith(
                bottom: MediaQuery.paddingOf(context).bottom.max(24.0),
              ),
              child: builder(context),
            ),
        ],
      ),
    );

    if (withBlurBackground) {
      child = AppBlurBackground(child: child);
    }

    if (backgroundBuilder case final builder?) {
      child = Stack(
        children: [
          builder(context),
          child,
        ],
      );
    }

    child = BrightnessLayer(
      brightness: estimateBrightness,
      child: Scaffold(
        backgroundColor: backgroundColor,
        body: child,
      ),
    );

    return child;
  }
}
