import 'package:flutter/material.dart';
import 'package:me_constants/me_constants.dart' show METhemeBuildContextExtension;
import 'package:pin_code_fields/pin_code_fields.dart';

class PinCodeInputWidget extends StatefulWidget {
  const PinCodeInputWidget({
    super.key,
    required this.length,
    required this.controller,
    required this.onCompleted,
    required this.onChanged,
    this.enabled = true,
  });

  final int length;
  final TextEditingController controller;
  final Function(String) onCompleted;
  final Function(String) onChanged;
  final bool enabled;

  @override
  State<PinCodeInputWidget> createState() => _PinCodeInputWidgetState();
}

class _PinCodeInputWidgetState extends State<PinCodeInputWidget> with WidgetsBindingObserver {
  TextEditingController get _controller => widget.controller;

  final _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _requestFocus();
    }
  }

  @override
  void dispose() {
    _focusNode.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  void _requestFocus() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && !_focusNode.hasFocus) {
        _focusNode.requestFocus();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final themeColor = context.themeColor;
    return PinCodeTextField(
      appContext: context,
      autoFocus: true,
      autoDisposeControllers: false,
      controller: _controller,
      enabled: widget.enabled,
      enableActiveFill: true,
      focusNode: _focusNode,
      length: widget.length,
      obscureText: false,
      animationType: AnimationType.fade,
      keyboardType: TextInputType.number,
      onChanged: widget.onChanged,
      onCompleted: widget.onCompleted,
      beforeTextPaste: (text) {
        return text != null && RegExp('^\\d{${widget.length}}\$').hasMatch(text);
      },
      pinTheme: PinTheme(
        shape: PinCodeFieldShape.box,
        borderRadius: BorderRadius.circular(8),
        fieldHeight: 60,
        fieldWidth: 50,
        activeFillColor: theme.cardColor,
        inactiveFillColor: theme.cardColor,
        selectedFillColor: theme.cardColor,
        activeColor: themeColor,
        inactiveColor: theme.disabledColor,
        selectedColor: themeColor,
      ),
    );
  }
}
