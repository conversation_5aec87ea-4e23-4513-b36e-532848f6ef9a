import 'dart:async';

import 'package:flutter/material.dart';
import 'package:me_constants/me_constants.dart';
import 'package:me_extensions/me_extensions.dart' show MEColorExtension;
import 'package:me_ui/me_ui.dart';

import '/res/assets.gen.dart';
import 'toast.dart';

class CopyButton extends StatefulWidget {
  const CopyButton({
    super.key,
    required this.onCopy,
    this.onCopyToast,
    this.dimension = 20.0,
    this.color,
    this.copiedColor,
  });

  final String? Function() onCopy;
  final String? Function()? onCopyToast;
  final double dimension;
  final Color? color;
  final Color? copiedColor;

  @override
  State<CopyButton> createState() => CopyButtonState();
}

class CopyButtonState extends State<CopyButton> {
  bool _copied = false;
  Timer? _timer;

  @override
  void dispose() {
    super.dispose();
    _timer?.cancel();
    _timer = null;
  }

  void _setTimer() {
    _timer?.cancel();
    _timer = Timer(const Duration(seconds: 3), () {
      setState(() {
        _copied = false;
      });
    });
  }

  void toggle() {
    setState(() {
      _copied = true;
    });
    final data = widget.onCopy();
    final toast = widget.onCopyToast?.call();
    copy(data);
    if (toast != null) {
      Card3ToastUtil.showToast(message: toast);
    }
    _setTimer();
  }

  @override
  Widget build(BuildContext context) {
    return RippleTap(
      onTap: toggle,
      width: widget.dimension,
      height: widget.dimension,
      borderRadius: context.meTheme.borderRadius,
      padding: EdgeInsets.all(widget.dimension / 6),
      child: AnimatedCrossFade(
        duration: kThemeAnimationDuration,
        crossFadeState: _copied ? CrossFadeState.showSecond : CrossFadeState.showFirst,
        firstChild: Assets.icons.buttonCopy.svg(
          colorFilter: widget.color?.filter,
          fit: BoxFit.cover,
          width: widget.dimension,
          height: widget.dimension,
        ),
        secondChild: FittedBox(
          fit: BoxFit.cover,
          child: Icon(
            Icons.check,
            color: widget.copiedColor ?? context.meTheme.successColor,
            size: widget.dimension,
          ),
        ),
      ),
    );
  }
}
