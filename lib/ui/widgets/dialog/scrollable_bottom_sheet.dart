import 'package:flutter/material.dart';
import 'package:me_extensions/me_extensions.dart';
import 'package:me_misc/me_misc.dart' show meContext;
import 'package:me_ui/me_ui.dart' as ui;

import '/res/assets.gen.dart';

class ScrollableBottomSheet extends StatelessWidget {
  const ScrollableBottomSheet({
    super.key,
    this.title,
    this.description,
    this.action,
    this.shape,
    this.boxConstraints,
    this.titleStyle,
    this.descriptionStyle,
    this.descriptionMatchedStyle,
    this.descriptionHighlightColor,
    this.onDescriptionMatched,
    this.sliversBuilder,
    this.iconBuilder,
    this.headerBuilder,
    this.headerPadding = const EdgeInsets.only(
      top: 12.0,
      bottom: 12.0,
      left: 12.0,
      right: 12.0,
    ),
    this.headerMaxHeight = 73.0,
    this.headerSpacing = 10.0,
    this.bottomBuilder,
    this.resizeToAvoidBottomInset = true,
    this.backgroundColor,
  });

  final String? title;
  final String? description;
  final Widget? action;
  final ShapeBorder? shape;
  final BoxConstraints? boxConstraints;
  final ui.SliversBuilder? sliversBuilder;
  final WidgetBuilder? iconBuilder;
  final WidgetBuilder? bottomBuilder;
  final WidgetBuilder? headerBuilder;
  final EdgeInsets? headerPadding;
  final double headerMaxHeight;
  final double headerSpacing;
  final TextStyle? titleStyle;
  final TextStyle? descriptionStyle;
  final TextStyle? descriptionMatchedStyle;
  final Color? descriptionHighlightColor;
  final InlineSpan Function(int position, Match matched)? onDescriptionMatched;
  final bool resizeToAvoidBottomInset;
  final Color? backgroundColor;

  static Future<T?> show<T>({
    Key? key,
    BuildContext? context,
    required WidgetBuilder builder,
    Color? backgroundColor,
    bool enableDrag = true,
    bool isDismissible = true,
    bool useSafeArea = false,
    BoxConstraints? constraints,
    String? routeName,
    RouteSettings? routeSettings,
    double? heightFactor,
  }) {
    context ??= meContext;
    if (routeSettings == null && routeName != null) {
      routeSettings = RouteSettings(name: routeName);
    }
    final child = builder(context);
    return showModalBottomSheet(
      context: context,
      backgroundColor: backgroundColor ?? Theme.of(context).bottomSheetTheme.backgroundColor,
      enableDrag: enableDrag,
      isDismissible: isDismissible,
      useSafeArea: useSafeArea,
      constraints: constraints ?? const BoxConstraints(maxWidth: 640.0),
      isScrollControlled: true,
      routeSettings: routeSettings,
      builder: (context) => constraints == null
          ? FractionallySizedBox(
              heightFactor: heightFactor ?? 700.0 / 896.0,
              child: child,
            )
          : child,
    );
  }

  @override
  Widget build(BuildContext context) {
    return ui.ScrollableBottomSheet(
      title: title,
      description: description,
      action: action,
      shape: shape,
      boxConstraints: boxConstraints,
      sliversBuilder: sliversBuilder,
      iconBuilder: (context) => ui.RippleTap(
        onTap: () => Navigator.of(context).maybePop(),
        width: 48.0,
        height: 48.0,
        padding: const EdgeInsets.all(12.0),
        shape: const CircleBorder(),
        child: Assets.icons.back.svg(
          fit: BoxFit.contain,
          colorFilter: Theme.of(context).textTheme.bodyMedium?.color?.filter,
        ),
      ),
      bottomBuilder: bottomBuilder,
      titleStyle: titleStyle,
      descriptionStyle: descriptionStyle,
      descriptionMatchedStyle: descriptionMatchedStyle,
      descriptionHighlightColor: descriptionHighlightColor,
      onDescriptionMatched: onDescriptionMatched,
      headerBuilder: headerBuilder,
      headerPadding: headerPadding,
      headerMaxHeight: headerMaxHeight,
      headerSpacing: headerSpacing,
      resizeToAvoidBottomInset: resizeToAvoidBottomInset,
      backgroundColor: backgroundColor ?? Theme.of(context).bottomSheetTheme.backgroundColor,
    );
  }
}
