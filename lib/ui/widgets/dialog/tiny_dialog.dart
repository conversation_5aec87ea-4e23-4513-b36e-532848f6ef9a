import 'package:flutter/material.dart';
import 'package:me_constants/me_constants.dart';
import 'package:me_l10n/me_l10n.dart';
import 'package:me_misc/me_misc.dart';

class TinyDialog extends StatelessWidget {
  const TinyDialog._({
    super.key,
    required this.text,
    this.captionText,
    this.buttons,
  });

  final String text;
  final String? captionText;
  final List<Widget> Function(BuildContext context)? buttons;

  static Future<T?> show<T>({
    BuildContext? context,
    Key? key,
    required String text,
    String? captionText,
    List<Widget> Function(BuildContext context)? buttons,
  }) {
    return showDialog<T>(
      context: context ?? meContext,
      builder: (context) => TinyDialog._(
        key: key,
        text: text,
        captionText: captionText,
        buttons: buttons,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Theme.of(context).colorScheme.surface,
      shape: RoundedRectangleBorder(
        borderRadius: context.meTheme.borderRadius,
      ),
      contentPadding: const EdgeInsetsDirectional.only(
        start: 24.0,
        end: 24.0,
        top: 20.0,
        bottom: 6.0,
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        spacing: 8.0,
        children: [
          Text(text),
          if (captionText case final text?)
            Text(text, style: Theme.of(context).textTheme.bodySmall),
          if (buttons case final buttons?)
            ...buttons(context)
          else
            TextButton(
              onPressed: Navigator.of(context).maybePop,
              child: Text(context.l10nME.okButton),
            ),
        ],
      ),
    );
  }
}
