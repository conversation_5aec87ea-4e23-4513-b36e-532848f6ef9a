import 'package:flutter/material.dart';
import 'package:me_extensions/me_extensions.dart';
import 'package:me_ui/me_ui.dart' show MEImage;

import '/internals/methods.dart' show handleExceptions;
import '/models/user.dart' show UserWithAvatar;

class UserAvatar extends StatelessWidget {
  const UserAvatar({
    super.key,
    required this.user,
    this.dimension,
    this.emptyBuilder,
    this.errorBuilder,
    this.borderRadius,
    this.backgroundColor,
    this.placeholderColor,
  });

  final UserWithAvatar? user;
  final double? dimension;
  final WidgetBuilder? emptyBuilder;
  final ImageErrorWidgetBuilder? errorBuilder;
  final BorderRadiusGeometry? borderRadius;
  final Color? backgroundColor;
  final Color? placeholderColor;

  @override
  Widget build(BuildContext context) {
    return MEImage(
      user?.avatar ?? '',
      clipOval: borderRadius == null,
      width: dimension,
      height: dimension,
      cacheWidth: dimension?.toCache(context),
      backgroundColor: backgroundColor ?? Theme.of(context).dividerColor,
      borderRadius: borderRadius ?? BorderRadius.zero,
      fit: BoxFit.cover,
      emptyBuilder: emptyBuilder ?? _buildPlaceholder,
      errorBuilder:
          errorBuilder ??
          (context, e, s) {
            handleExceptions(error: e, stackTrace: s);
            return _buildPlaceholder(context);
          },
    );
  }

  Widget _buildPlaceholder(BuildContext context) {
    return FittedBox(
      fit: BoxFit.cover,
      child: Icon(Icons.account_circle, color: placeholderColor),
    );
  }
}
