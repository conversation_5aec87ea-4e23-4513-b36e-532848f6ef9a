import 'package:flutter/material.dart';
import 'package:me_misc/me_misc.dart';

import '/api/front_end.dart'
    show FrontEndGitHubContributionCollection, FrontEndGitHubContributionDays, FrontEndGitHubContributionDay;
import '/res/colors.gen.dart';

const _dayLabels = ['Mon', 'Wed', 'Fri'];
const _dayLabelStyle = TextStyle(
  fontSize: 12,
  color: ColorName.captionTextColorDark,
);

class GitHubContributionGraph extends StatelessWidget {
  const GitHubContributionGraph({
    super.key,
    required this.collection,
    this.cellSize = 10.0,
    this.cellSpacing = 3.0,
    this.showMonthLabels = true,
    this.showDayLabels = true,
    this.showLegend = true,
  });

  final FrontEndGitHubContributionCollection collection;
  final double cellSize;
  final double cellSpacing;
  final bool showMonthLabels;
  final bool showDayLabels;
  final bool showLegend;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth > 600) {
          return _buildFullGraph();
        } else {
          return _buildScrollableGraph();
        }
      },
    );
  }

  Widget _buildFullGraph() {
    return ConstrainedBox(
      constraints: const BoxConstraints(maxWidth: 800),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (showMonthLabels) _buildMonthLabels(),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (showDayLabels) _buildDayLabels(),
              Expanded(child: _buildContributionGrid()),
            ],
          ),
          if (showLegend) _buildLegend(),
        ],
      ),
    );
  }

  Widget _buildScrollableGraph() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 使用一个统一的滚动容器包含月份标签和贡献图
        Row(
          spacing: 4.0,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 左侧固定的星期标签
            if (showDayLabels) _buildDayLabelsWithMonthSpace(),
            // 右侧可滚动的内容（月份标签 + 贡献图）
            Expanded(
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                physics: const ClampingScrollPhysics(),
                child: _buildScrollableContent(),
              ),
            ),
          ],
        ),
        if (showLegend) _buildLegend(),
      ],
    );
  }

  Widget _buildMonthLabels() {
    return Row(
      children: [
        if (showDayLabels) SizedBox(width: _dayLabelWidth),
        Expanded(child: _buildMonthLabelsContent()),
      ],
    );
  }

  Widget _buildMonthLabelsContent() {
    final weeks = collection.calendar.weeks;
    final monthLabels = <Widget>[];

    String? currentMonth;
    int weekIndex = 0;

    for (final week in weeks) {
      if (week.days.isNotEmpty) {
        final firstDay = week.days.first;
        final month = _getMonthAbbreviation(firstDay.date.month);

        if (currentMonth != month) {
          monthLabels.add(
            Positioned(
              left: weekIndex * (cellSize + cellSpacing),
              child: Text(
                month,
                style: const TextStyle(
                  fontSize: 12,
                  color: ColorName.captionTextColorDark,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          );
          currentMonth = month;
        }
      }
      weekIndex++;
    }

    return SizedBox(
      height: 20,
      width: weeks.length * (cellSize + cellSpacing),
      child: Stack(children: monthLabels),
    );
  }

  Widget _buildDayLabels() {
    return SizedBox(
      width: _dayLabelWidth,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          const SizedBox(height: 20),
          ...List.generate(7, (index) {
            final shouldShow = index == 1 || index == 3 || index == 5; // Mon, Wed, Fri
            return Container(
              height: cellSize,
              margin: EdgeInsets.only(bottom: cellSpacing),
              alignment: Alignment.centerRight,
              child: shouldShow
                  ? FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(
                        _dayLabels[index ~/ 2],
                        style: const TextStyle(
                          fontSize: 12,
                          color: ColorName.captionTextColorDark,
                        ),
                      ),
                    )
                  : null,
            );
          }),
        ],
      ),
    );
  }

  Widget _buildDayLabelsWithMonthSpace() {
    return SizedBox(
      width: _dayLabelWidth,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          const SizedBox(height: 20.0),
          ...List.generate(7, (index) {
            final shouldShow = index == 1 || index == 3 || index == 5; // Mon, Wed, Fri
            return Container(
              height: cellSize,
              margin: EdgeInsets.only(bottom: cellSpacing),
              alignment: Alignment.centerRight,
              child: shouldShow
                  ? FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(_dayLabels[index ~/ 2], style: _dayLabelStyle),
                    )
                  : null,
            );
          }),
        ],
      ),
    );
  }

  Widget _buildScrollableContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 月份标签
        if (showMonthLabels) _buildMonthLabelsContent(),
        if (!showMonthLabels) const SizedBox(height: 20),
        // 贡献图
        _buildContributionGridOnly(),
      ],
    );
  }

  Widget _buildContributionGrid() {
    final weeks = collection.calendar.weeks;

    return SizedBox(
      width: weeks.length * (cellSize + cellSpacing),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: weeks.map((week) => _buildWeekColumn(week)).toList(),
      ),
    );
  }

  Widget _buildContributionGridOnly() {
    final weeks = collection.calendar.weeks;

    return SizedBox(
      width: weeks.length * (cellSize + cellSpacing),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: weeks.map((week) => _buildWeekColumn(week)).toList(),
      ),
    );
  }

  Widget _buildWeekColumn(FrontEndGitHubContributionDays week) {
    return Padding(
      padding: EdgeInsets.only(right: cellSpacing),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: week.days.map((day) => _buildDayCell(day)).toList(),
      ),
    );
  }

  Widget _buildDayCell(FrontEndGitHubContributionDay day) {
    final intensity = _getContributionIntensity(day.contributionCount);

    return Container(
      width: cellSize,
      height: cellSize,
      margin: EdgeInsets.only(bottom: cellSpacing),
      decoration: BoxDecoration(
        color: _getContributionColor(intensity),
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Widget _buildLegend() {
    return Padding(
      padding: const EdgeInsets.only(top: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          const Text(
            'Less',
            style: TextStyle(
              fontSize: 12,
              color: ColorName.captionTextColorDark,
            ),
          ),
          const SizedBox(width: 8),
          ...List.generate(5, (index) {
            return Container(
              width: 10,
              height: 10,
              margin: const EdgeInsets.only(left: 2),
              decoration: BoxDecoration(
                color: _getContributionColor(index),
                borderRadius: BorderRadius.circular(2),
              ),
            );
          }),
          const SizedBox(width: 8),
          const Text(
            'More',
            style: TextStyle(
              fontSize: 12,
              color: ColorName.captionTextColorDark,
            ),
          ),
        ],
      ),
    );
  }

  double get _dayLabelWidth =>
      _dayLabels.map((e) => calcTextSize(e, _dayLabelStyle).width).reduce((a, b) => a > b ? a : b);

  // 辅助方法
  int _getContributionIntensity(int count) {
    if (count == 0) {
      return 0;
    }
    if (count <= 3) {
      return 1;
    }
    if (count <= 6) {
      return 2;
    }
    if (count <= 9) {
      return 3;
    }
    return 4;
  }

  Color _getContributionColor(int intensity) {
    switch (intensity) {
      case 1:
        return const Color(0xFF033A16);
      case 2:
        return const Color(0xFF196C2E);
      case 3:
        return const Color(0xFF2EA043);
      case 4:
        return const Color(0xFF56D364);
      default:
        return const Color(0xFF151B23);
    }
  }

  String _getMonthAbbreviation(int month) {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return months[month - 1];
  }
}
