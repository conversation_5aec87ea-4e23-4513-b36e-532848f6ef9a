import 'dart:async' show Completer;

import 'package:collection/collection.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '/api/config.dart' show Config;
import '/internals/box.dart' show BoxCaches;
import '/internals/methods.dart' show handleExceptions;
import '/models/business.dart' show Message, Network, Point;
import '/provider/api.dart' show Paged, RefExtension, apiAstroxProvider, apiConfigProvider, apiServiceProvider;

part 'business.g.dart';

// 配置缓存键
const String _configCacheKey = 'card3_config_cache';
// 配置缓存有效期 - 10分钟（毫秒）
const int _configCacheExpiration = 10 * 60 * 1000;

final configProvider = Provider.autoDispose<Config>((ref) => ref.watch(configStateProvider));

@Riverpod(keepAlive: true)
class ConfigState extends _$ConfigState {
  // 默认配置
  static final _defaultConfigData = Config.fromJson({
    'chainTokenFilters': {
      '1': ['ETH', 'USDC', 'USDT'],
      '8453': ['ETH', 'USDT', 'USDC', 'DEGEN'],
      '137': ['POL', 'USDC', 'USDT'],
      '56': ['BNB', 'BUSD', 'WBNB'],
      '42161': ['ETH', 'USDC', 'USDT'],
      '10': ['ETH', 'USDC', 'USDT'],
      '43114': ['ETH', 'USDC', 'USDT'],
      '59144': ['ETH', 'USDC', 'USDT'],
    },
    'chainFilters': [10, 324, 59144],
    'ethccEventIds': <int>[0],
    'defaultMode': 'ETHCC',
    'nfc': false,
    'okx': true, // 默认启用 OKX API
    'events': [],
  });

  @override
  Config build() {
    loadConfig();
    return _getConfigFromCache() ?? _defaultConfigData;
  }

  static Completer<Config>? _loadLock;

  static Config? _getConfigFromCache() {
    try {
      final cachedData = BoxCaches.get(_configCacheKey);
      if (cachedData case final Map cacheMap) {
        // 如果缓存未过期
        final timestamp = int.parse(cacheMap['timestamp'].toString());
        if (DateTime.now().millisecondsSinceEpoch - timestamp < _configCacheExpiration) {
          final configData = cacheMap['config'] as Map;
          return Config.fromJson(configData.cast());
        }
      }
      return null;
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      BoxCaches.delete(_configCacheKey);
      return null;
    }
  }

  static Future<Config> _loadConfig(Ref ref) async {
    if (_loadLock case final lock?) {
      return lock.future;
    }
    final lock = _loadLock = Completer<Config>();
    ref
        .read(apiConfigProvider)
        .getConfig()
        .then(
          lock.complete,
          onError: (e, s) {
            lock.completeError(e, s);
          },
        )
        .whenComplete(() {
          _loadLock = null;
        });
    return lock.future;
  }

  Future<Config> loadConfig() async {
    final cachedConfig = _getConfigFromCache();
    final config = await _loadConfig(ref);
    if (config != cachedConfig) {
      await BoxCaches.put(
        _configCacheKey,
        {
          'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
          'config': config.toJson(), // 存储原始数据而不是对象
        },
      );
      state = config;
    }
    return state;
  }
}

@riverpod
Future<List<Network>> fetchNetworksNew(Ref ref) async {
  late final Config config;
  late final List<Network> networks;
  await Future.wait([
    ref.read(configStateProvider.notifier).loadConfig().then((value) => config = value),
    ref.read(apiAstroxProvider).op.getNetworks().then((value) => networks = value),
  ]);
  final filtered = networks
      .where((e) => e.network != 'IC')
      .where((e) => !config.isNetworkUnsupported(e))
      .sorted((a, b) => b.weight.compareTo(a.weight));
  return filtered;
}

@riverpod
Future<Paged<Message>> fetchMessages(
  Ref ref, {
  required int page,
}) {
  final ct = ref.cancelToken();
  ref.onDispose(() {
    ct.cancel();
  });
  return ref.read(apiServiceProvider).listMessages(page: page, cancelToken: ct);
}

@riverpod
Future<Paged<Point>> fetchPoints(
  Ref ref, {
  required int pageNum,
  required int pageSize,
}) async {
  final result = await ref.read(apiServiceProvider).getPoints(pageNum: pageNum, pageSize: pageSize);
  return result;
}
