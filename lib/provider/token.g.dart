// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'token.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchRawWalletPortfolioOKXHash() =>
    r'cd46a7c28b095c1620de752f539e56f5022faab2';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [fetchRawWalletPortfolioOKX].
@ProviderFor(fetchRawWalletPortfolioOKX)
const fetchRawWalletPortfolioOKXProvider = FetchRawWalletPortfolioOKXFamily();

/// See also [fetchRawWalletPortfolioOKX].
class FetchRawWalletPortfolioOKXFamily
    extends Family<AsyncValue<WalletPortfolioOKX2>> {
  /// See also [fetchRawWalletPortfolioOKX].
  const FetchRawWalletPortfolioOKXFamily();

  /// See also [fetchRawWalletPortfolioOKX].
  FetchRawWalletPortfolioOKXProvider call({
    required Network? network,
    required String? address,
    bool excludeRiskType = true,
    bool writeToCache = true,
  }) {
    return FetchRawWalletPortfolioOKXProvider(
      network: network,
      address: address,
      excludeRiskType: excludeRiskType,
      writeToCache: writeToCache,
    );
  }

  @override
  FetchRawWalletPortfolioOKXProvider getProviderOverride(
    covariant FetchRawWalletPortfolioOKXProvider provider,
  ) {
    return call(
      network: provider.network,
      address: provider.address,
      excludeRiskType: provider.excludeRiskType,
      writeToCache: provider.writeToCache,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchRawWalletPortfolioOKXProvider';
}

/// See also [fetchRawWalletPortfolioOKX].
class FetchRawWalletPortfolioOKXProvider
    extends AutoDisposeFutureProvider<WalletPortfolioOKX2> {
  /// See also [fetchRawWalletPortfolioOKX].
  FetchRawWalletPortfolioOKXProvider({
    required Network? network,
    required String? address,
    bool excludeRiskType = true,
    bool writeToCache = true,
  }) : this._internal(
         (ref) => fetchRawWalletPortfolioOKX(
           ref as FetchRawWalletPortfolioOKXRef,
           network: network,
           address: address,
           excludeRiskType: excludeRiskType,
           writeToCache: writeToCache,
         ),
         from: fetchRawWalletPortfolioOKXProvider,
         name: r'fetchRawWalletPortfolioOKXProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$fetchRawWalletPortfolioOKXHash,
         dependencies: FetchRawWalletPortfolioOKXFamily._dependencies,
         allTransitiveDependencies:
             FetchRawWalletPortfolioOKXFamily._allTransitiveDependencies,
         network: network,
         address: address,
         excludeRiskType: excludeRiskType,
         writeToCache: writeToCache,
       );

  FetchRawWalletPortfolioOKXProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.network,
    required this.address,
    required this.excludeRiskType,
    required this.writeToCache,
  }) : super.internal();

  final Network? network;
  final String? address;
  final bool excludeRiskType;
  final bool writeToCache;

  @override
  Override overrideWith(
    FutureOr<WalletPortfolioOKX2> Function(
      FetchRawWalletPortfolioOKXRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchRawWalletPortfolioOKXProvider._internal(
        (ref) => create(ref as FetchRawWalletPortfolioOKXRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        network: network,
        address: address,
        excludeRiskType: excludeRiskType,
        writeToCache: writeToCache,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<WalletPortfolioOKX2> createElement() {
    return _FetchRawWalletPortfolioOKXProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchRawWalletPortfolioOKXProvider &&
        other.network == network &&
        other.address == address &&
        other.excludeRiskType == excludeRiskType &&
        other.writeToCache == writeToCache;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, network.hashCode);
    hash = _SystemHash.combine(hash, address.hashCode);
    hash = _SystemHash.combine(hash, excludeRiskType.hashCode);
    hash = _SystemHash.combine(hash, writeToCache.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchRawWalletPortfolioOKXRef
    on AutoDisposeFutureProviderRef<WalletPortfolioOKX2> {
  /// The parameter `network` of this provider.
  Network? get network;

  /// The parameter `address` of this provider.
  String? get address;

  /// The parameter `excludeRiskType` of this provider.
  bool get excludeRiskType;

  /// The parameter `writeToCache` of this provider.
  bool get writeToCache;
}

class _FetchRawWalletPortfolioOKXProviderElement
    extends AutoDisposeFutureProviderElement<WalletPortfolioOKX2>
    with FetchRawWalletPortfolioOKXRef {
  _FetchRawWalletPortfolioOKXProviderElement(super.provider);

  @override
  Network? get network =>
      (origin as FetchRawWalletPortfolioOKXProvider).network;
  @override
  String? get address => (origin as FetchRawWalletPortfolioOKXProvider).address;
  @override
  bool get excludeRiskType =>
      (origin as FetchRawWalletPortfolioOKXProvider).excludeRiskType;
  @override
  bool get writeToCache =>
      (origin as FetchRawWalletPortfolioOKXProvider).writeToCache;
}

String _$fetchRawWalletPortfolioAstroxHash() =>
    r'be307cd397fb6ed09d6c2c4b85f5d13243ed6f62';

/// See also [fetchRawWalletPortfolioAstrox].
@ProviderFor(fetchRawWalletPortfolioAstrox)
const fetchRawWalletPortfolioAstroxProvider =
    FetchRawWalletPortfolioAstroxFamily();

/// See also [fetchRawWalletPortfolioAstrox].
class FetchRawWalletPortfolioAstroxFamily
    extends Family<AsyncValue<WalletPortfolioAstrox>> {
  /// See also [fetchRawWalletPortfolioAstrox].
  const FetchRawWalletPortfolioAstroxFamily();

  /// See also [fetchRawWalletPortfolioAstrox].
  FetchRawWalletPortfolioAstroxProvider call({
    required Network? network,
    required String? address,
    bool writeToCache = true,
  }) {
    return FetchRawWalletPortfolioAstroxProvider(
      network: network,
      address: address,
      writeToCache: writeToCache,
    );
  }

  @override
  FetchRawWalletPortfolioAstroxProvider getProviderOverride(
    covariant FetchRawWalletPortfolioAstroxProvider provider,
  ) {
    return call(
      network: provider.network,
      address: provider.address,
      writeToCache: provider.writeToCache,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchRawWalletPortfolioAstroxProvider';
}

/// See also [fetchRawWalletPortfolioAstrox].
class FetchRawWalletPortfolioAstroxProvider
    extends AutoDisposeFutureProvider<WalletPortfolioAstrox> {
  /// See also [fetchRawWalletPortfolioAstrox].
  FetchRawWalletPortfolioAstroxProvider({
    required Network? network,
    required String? address,
    bool writeToCache = true,
  }) : this._internal(
         (ref) => fetchRawWalletPortfolioAstrox(
           ref as FetchRawWalletPortfolioAstroxRef,
           network: network,
           address: address,
           writeToCache: writeToCache,
         ),
         from: fetchRawWalletPortfolioAstroxProvider,
         name: r'fetchRawWalletPortfolioAstroxProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$fetchRawWalletPortfolioAstroxHash,
         dependencies: FetchRawWalletPortfolioAstroxFamily._dependencies,
         allTransitiveDependencies:
             FetchRawWalletPortfolioAstroxFamily._allTransitiveDependencies,
         network: network,
         address: address,
         writeToCache: writeToCache,
       );

  FetchRawWalletPortfolioAstroxProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.network,
    required this.address,
    required this.writeToCache,
  }) : super.internal();

  final Network? network;
  final String? address;
  final bool writeToCache;

  @override
  Override overrideWith(
    FutureOr<WalletPortfolioAstrox> Function(
      FetchRawWalletPortfolioAstroxRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchRawWalletPortfolioAstroxProvider._internal(
        (ref) => create(ref as FetchRawWalletPortfolioAstroxRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        network: network,
        address: address,
        writeToCache: writeToCache,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<WalletPortfolioAstrox> createElement() {
    return _FetchRawWalletPortfolioAstroxProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchRawWalletPortfolioAstroxProvider &&
        other.network == network &&
        other.address == address &&
        other.writeToCache == writeToCache;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, network.hashCode);
    hash = _SystemHash.combine(hash, address.hashCode);
    hash = _SystemHash.combine(hash, writeToCache.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchRawWalletPortfolioAstroxRef
    on AutoDisposeFutureProviderRef<WalletPortfolioAstrox> {
  /// The parameter `network` of this provider.
  Network? get network;

  /// The parameter `address` of this provider.
  String? get address;

  /// The parameter `writeToCache` of this provider.
  bool get writeToCache;
}

class _FetchRawWalletPortfolioAstroxProviderElement
    extends AutoDisposeFutureProviderElement<WalletPortfolioAstrox>
    with FetchRawWalletPortfolioAstroxRef {
  _FetchRawWalletPortfolioAstroxProviderElement(super.provider);

  @override
  Network? get network =>
      (origin as FetchRawWalletPortfolioAstroxProvider).network;
  @override
  String? get address =>
      (origin as FetchRawWalletPortfolioAstroxProvider).address;
  @override
  bool get writeToCache =>
      (origin as FetchRawWalletPortfolioAstroxProvider).writeToCache;
}

String _$fetchWalletPortfolioHash() =>
    r'e95d2495aecd1ca968a0b3099a680d83f9734b36';

/// See also [fetchWalletPortfolio].
@ProviderFor(fetchWalletPortfolio)
final fetchWalletPortfolioProvider =
    AutoDisposeStreamProvider<WalletPortfolio>.internal(
      fetchWalletPortfolio,
      name: r'fetchWalletPortfolioProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$fetchWalletPortfolioHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchWalletPortfolioRef = AutoDisposeStreamProviderRef<WalletPortfolio>;
String _$fetchSOLBalanceFromPortfolioOKXHash() =>
    r'534072e0e1c64923c0dfc143faecfd6bce9e6ac3';

/// See also [fetchSOLBalanceFromPortfolioOKX].
@ProviderFor(fetchSOLBalanceFromPortfolioOKX)
final fetchSOLBalanceFromPortfolioOKXProvider =
    AutoDisposeProvider<Decimal?>.internal(
      fetchSOLBalanceFromPortfolioOKX,
      name: r'fetchSOLBalanceFromPortfolioOKXProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$fetchSOLBalanceFromPortfolioOKXHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchSOLBalanceFromPortfolioOKXRef = AutoDisposeProviderRef<Decimal?>;
String _$tokenBalanceStreamHash() =>
    r'8e7f51668d09298630bf0bb484bbb28600abe18d';

/// See also [tokenBalanceStream].
@ProviderFor(tokenBalanceStream)
const tokenBalanceStreamProvider = TokenBalanceStreamFamily();

/// See also [tokenBalanceStream].
class TokenBalanceStreamFamily extends Family<AsyncValue<TokenAmount?>> {
  /// See also [tokenBalanceStream].
  const TokenBalanceStreamFamily();

  /// See also [tokenBalanceStream].
  TokenBalanceStreamProvider call({
    required Network? network,
    required String? address,
  }) {
    return TokenBalanceStreamProvider(network: network, address: address);
  }

  @override
  TokenBalanceStreamProvider getProviderOverride(
    covariant TokenBalanceStreamProvider provider,
  ) {
    return call(network: provider.network, address: provider.address);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'tokenBalanceStreamProvider';
}

/// See also [tokenBalanceStream].
class TokenBalanceStreamProvider
    extends AutoDisposeStreamProvider<TokenAmount?> {
  /// See also [tokenBalanceStream].
  TokenBalanceStreamProvider({
    required Network? network,
    required String? address,
  }) : this._internal(
         (ref) => tokenBalanceStream(
           ref as TokenBalanceStreamRef,
           network: network,
           address: address,
         ),
         from: tokenBalanceStreamProvider,
         name: r'tokenBalanceStreamProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$tokenBalanceStreamHash,
         dependencies: TokenBalanceStreamFamily._dependencies,
         allTransitiveDependencies:
             TokenBalanceStreamFamily._allTransitiveDependencies,
         network: network,
         address: address,
       );

  TokenBalanceStreamProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.network,
    required this.address,
  }) : super.internal();

  final Network? network;
  final String? address;

  @override
  Override overrideWith(
    Stream<TokenAmount?> Function(TokenBalanceStreamRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: TokenBalanceStreamProvider._internal(
        (ref) => create(ref as TokenBalanceStreamRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        network: network,
        address: address,
      ),
    );
  }

  @override
  AutoDisposeStreamProviderElement<TokenAmount?> createElement() {
    return _TokenBalanceStreamProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TokenBalanceStreamProvider &&
        other.network == network &&
        other.address == address;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, network.hashCode);
    hash = _SystemHash.combine(hash, address.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TokenBalanceStreamRef on AutoDisposeStreamProviderRef<TokenAmount?> {
  /// The parameter `network` of this provider.
  Network? get network;

  /// The parameter `address` of this provider.
  String? get address;
}

class _TokenBalanceStreamProviderElement
    extends AutoDisposeStreamProviderElement<TokenAmount?>
    with TokenBalanceStreamRef {
  _TokenBalanceStreamProviderElement(super.provider);

  @override
  Network? get network => (origin as TokenBalanceStreamProvider).network;
  @override
  String? get address => (origin as TokenBalanceStreamProvider).address;
}

String _$fetchTokensHash() => r'341b86a0b8de51d53f63697319339702ecc5865c';

/// See also [fetchTokens].
@ProviderFor(fetchTokens)
const fetchTokensProvider = FetchTokensFamily();

/// See also [fetchTokens].
class FetchTokensFamily
    extends Family<AsyncValue<AstroxPaged<AstroxTokenConfig>>> {
  /// See also [fetchTokens].
  const FetchTokensFamily();

  /// See also [fetchTokens].
  FetchTokensProvider call({
    required int pageNum,
    required int pageSize,
    required String blockChain,
  }) {
    return FetchTokensProvider(
      pageNum: pageNum,
      pageSize: pageSize,
      blockChain: blockChain,
    );
  }

  @override
  FetchTokensProvider getProviderOverride(
    covariant FetchTokensProvider provider,
  ) {
    return call(
      pageNum: provider.pageNum,
      pageSize: provider.pageSize,
      blockChain: provider.blockChain,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchTokensProvider';
}

/// See also [fetchTokens].
class FetchTokensProvider
    extends AutoDisposeFutureProvider<AstroxPaged<AstroxTokenConfig>> {
  /// See also [fetchTokens].
  FetchTokensProvider({
    required int pageNum,
    required int pageSize,
    required String blockChain,
  }) : this._internal(
         (ref) => fetchTokens(
           ref as FetchTokensRef,
           pageNum: pageNum,
           pageSize: pageSize,
           blockChain: blockChain,
         ),
         from: fetchTokensProvider,
         name: r'fetchTokensProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$fetchTokensHash,
         dependencies: FetchTokensFamily._dependencies,
         allTransitiveDependencies:
             FetchTokensFamily._allTransitiveDependencies,
         pageNum: pageNum,
         pageSize: pageSize,
         blockChain: blockChain,
       );

  FetchTokensProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.pageNum,
    required this.pageSize,
    required this.blockChain,
  }) : super.internal();

  final int pageNum;
  final int pageSize;
  final String blockChain;

  @override
  Override overrideWith(
    FutureOr<AstroxPaged<AstroxTokenConfig>> Function(FetchTokensRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchTokensProvider._internal(
        (ref) => create(ref as FetchTokensRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        pageNum: pageNum,
        pageSize: pageSize,
        blockChain: blockChain,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<AstroxPaged<AstroxTokenConfig>>
  createElement() {
    return _FetchTokensProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchTokensProvider &&
        other.pageNum == pageNum &&
        other.pageSize == pageSize &&
        other.blockChain == blockChain;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, pageNum.hashCode);
    hash = _SystemHash.combine(hash, pageSize.hashCode);
    hash = _SystemHash.combine(hash, blockChain.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchTokensRef
    on AutoDisposeFutureProviderRef<AstroxPaged<AstroxTokenConfig>> {
  /// The parameter `pageNum` of this provider.
  int get pageNum;

  /// The parameter `pageSize` of this provider.
  int get pageSize;

  /// The parameter `blockChain` of this provider.
  String get blockChain;
}

class _FetchTokensProviderElement
    extends AutoDisposeFutureProviderElement<AstroxPaged<AstroxTokenConfig>>
    with FetchTokensRef {
  _FetchTokensProviderElement(super.provider);

  @override
  int get pageNum => (origin as FetchTokensProvider).pageNum;
  @override
  int get pageSize => (origin as FetchTokensProvider).pageSize;
  @override
  String get blockChain => (origin as FetchTokensProvider).blockChain;
}

String _$fetchRawTokensHash() => r'997ce749f6e7ce610024a020721fc2d2abaa7612';

/// See also [fetchRawTokens].
@ProviderFor(fetchRawTokens)
final fetchRawTokensProvider =
    AutoDisposeStreamProvider<List<AstroxTokenConfig>>.internal(
      fetchRawTokens,
      name: r'fetchRawTokensProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$fetchRawTokensHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchRawTokensRef =
    AutoDisposeStreamProviderRef<List<AstroxTokenConfig>>;
String _$getTokenPriceHash() => r'428f3cb89f61cbe1d401aa47f9dc70c11170b687';

/// See also [_getTokenPrice].
@ProviderFor(_getTokenPrice)
const _getTokenPriceProvider = _GetTokenPriceFamily();

/// See also [_getTokenPrice].
class _GetTokenPriceFamily extends Family<AsyncValue<Decimal?>> {
  /// See also [_getTokenPrice].
  const _GetTokenPriceFamily();

  /// See also [_getTokenPrice].
  _GetTokenPriceProvider call({
    required String symbol,
    required String chainName,
    required String ucid,
  }) {
    return _GetTokenPriceProvider(
      symbol: symbol,
      chainName: chainName,
      ucid: ucid,
    );
  }

  @override
  _GetTokenPriceProvider getProviderOverride(
    covariant _GetTokenPriceProvider provider,
  ) {
    return call(
      symbol: provider.symbol,
      chainName: provider.chainName,
      ucid: provider.ucid,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'_getTokenPriceProvider';
}

/// See also [_getTokenPrice].
class _GetTokenPriceProvider extends AutoDisposeFutureProvider<Decimal?> {
  /// See also [_getTokenPrice].
  _GetTokenPriceProvider({
    required String symbol,
    required String chainName,
    required String ucid,
  }) : this._internal(
         (ref) => _getTokenPrice(
           ref as _GetTokenPriceRef,
           symbol: symbol,
           chainName: chainName,
           ucid: ucid,
         ),
         from: _getTokenPriceProvider,
         name: r'_getTokenPriceProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$getTokenPriceHash,
         dependencies: _GetTokenPriceFamily._dependencies,
         allTransitiveDependencies:
             _GetTokenPriceFamily._allTransitiveDependencies,
         symbol: symbol,
         chainName: chainName,
         ucid: ucid,
       );

  _GetTokenPriceProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.symbol,
    required this.chainName,
    required this.ucid,
  }) : super.internal();

  final String symbol;
  final String chainName;
  final String ucid;

  @override
  Override overrideWith(
    FutureOr<Decimal?> Function(_GetTokenPriceRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: _GetTokenPriceProvider._internal(
        (ref) => create(ref as _GetTokenPriceRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        symbol: symbol,
        chainName: chainName,
        ucid: ucid,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Decimal?> createElement() {
    return _GetTokenPriceProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is _GetTokenPriceProvider &&
        other.symbol == symbol &&
        other.chainName == chainName &&
        other.ucid == ucid;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, symbol.hashCode);
    hash = _SystemHash.combine(hash, chainName.hashCode);
    hash = _SystemHash.combine(hash, ucid.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin _GetTokenPriceRef on AutoDisposeFutureProviderRef<Decimal?> {
  /// The parameter `symbol` of this provider.
  String get symbol;

  /// The parameter `chainName` of this provider.
  String get chainName;

  /// The parameter `ucid` of this provider.
  String get ucid;
}

class _GetTokenPriceProviderElement
    extends AutoDisposeFutureProviderElement<Decimal?>
    with _GetTokenPriceRef {
  _GetTokenPriceProviderElement(super.provider);

  @override
  String get symbol => (origin as _GetTokenPriceProvider).symbol;
  @override
  String get chainName => (origin as _GetTokenPriceProvider).chainName;
  @override
  String get ucid => (origin as _GetTokenPriceProvider).ucid;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
