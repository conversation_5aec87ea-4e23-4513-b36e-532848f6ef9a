import 'dart:io' as io;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:me_extensions/me_extensions.dart';
import 'package:me_misc/me_misc.dart' show MutexQueue;
import 'package:me_utils/me_utils.dart' show LogUtil;
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '/api/astrox.dart';
import '/api/config.dart' show TokenSymbolsMapping, TokenSymbolProvider;
import '/api/okx.dart' show OKXWalletProfileSummary, OKXWalletProfileToken, WalletPortfolioOKX2;
import '/api/solana.dart' as sol;
import '/constants/constants.dart' show tokenSOLAddress;
import '/extensions/riverpod_extension.dart';
import '/internals/box.dart' show BoxCaches;
import '/internals/methods.dart' show handleExceptions;
import '/models/business.dart' show Network, TokenAmount, WalletPortfolio;
import '/services/chain_manager.dart' show ChainManager;
import 'api.dart' show apiAstroxProvider, apiConfigProvider, apiOKXProvider, apiServiceProvider;
import 'business.dart' show configProvider;
import 'chain.dart';

part 'token.g.dart';

final balanceDiffProvider = Provider<String>(
  (ref) {
    final network = ref.watch(networkProvider);
    final date = DateTime.now();
    LogUtil.dd(
      () => 'Storing balance diff provider: $date',
      tag: 'balanceDiffProvider',
      tagWithTrace: false,
    );
    return '$network:$date';
  },
);

void refreshTokenBalances(dynamic ref) {
  ref.invalidate(balanceDiffProvider);
}

final tokenSymbolsMappingProvider = FutureProvider<TokenSymbolsMapping>((ref) {
  ref.watch(balanceDiffProvider);
  return ref.read(apiConfigProvider).getTokenSymbolsMapping();
});

const _walletPortfolioCachePrefixOKX = 'wallet_portfolio_okx:v1';
const _walletPortfolioCachePrefixAstroX = 'wallet_portfolio_astrox:v1';

final _tokenBalanceMutex = MutexQueue(2, tag: 'TokenBalanceMutex');

Future<void> writeWalletPortfolioOKXCache(
  Network network,
  String? address,
  WalletPortfolioOKX2? value,
) async {
  if (address == null || address.isEmpty) {
    return;
  }
  const cachePrefix = _walletPortfolioCachePrefixOKX;
  final cacheKey = '$cachePrefix:$address:${network.chainIndexOKX}';
  if (value == null) {
    await BoxCaches.delete(cacheKey);
  } else {
    await BoxCaches.put(cacheKey, value.toJson().serialize());
  }
}

@riverpod
Future<WalletPortfolioOKX2> fetchRawWalletPortfolioOKX(
  Ref ref, {
  required Network? network,
  required String? address,
  bool excludeRiskType = true,
  bool writeToCache = true,
}) async {
  ref.watch(balanceDiffProvider);

  if (address == null || address.isEmpty || network == null || network.chainIndexOKX.isEmpty) {
    return WalletPortfolioOKX2.empty;
  }

  final ct = ref.cancelToken();
  ref.onDispose(() {
    ct.cancel();
  });

  late final OKXWalletProfileSummary summary;
  late final Map<String, String>? symbolsMapping;
  late List<OKXWalletProfileToken> tokens;

  await Future.wait([
    ref.read(tokenSymbolsMappingProvider.future).then((r) => symbolsMapping = r[TokenSymbolProvider.okx]),
    ref
        .read(apiOKXProvider)
        .getWalletProfile(
          address: address,
          chainIdOKX: network.chainIndexOKX,
          excludeRiskType: excludeRiskType,
          cancelToken: ct,
        )
        .then((value) => (summary, tokens) = value),
  ]);

  // Replace every matching tokens' symbol.
  if (symbolsMapping case final mapping?) {
    tokens = tokens.map((e) {
      final symbolMapping = mapping[e.symbol];
      if (symbolMapping != null) {
        LogUtil.w(
          'OKX symbol mapping: ${e.symbol} -> $symbolMapping',
          tag: 'fetchRawWalletPortfolioOKX',
          tagWithTrace: false,
        );
        return e.copyWith(symbol: symbolMapping);
      }
      return e;
    }).toList();
  }

  final result = WalletPortfolioOKX2(
    summary: summary,
    tokens: tokens,
  );
  if (writeToCache) {
    await writeWalletPortfolioOKXCache(network, address, result);
  }
  return result;
}

Future<void> writeWalletPortfolioAstroxCache(
  Network network,
  String? address,
  WalletPortfolioAstrox? value,
) async {
  if (address == null || address.isEmpty) {
    return;
  }
  const cachePrefix = _walletPortfolioCachePrefixAstroX;
  final cacheKey = '$cachePrefix:$address:${network.network}';
  if (value == null) {
    await BoxCaches.delete(cacheKey);
  } else {
    await BoxCaches.put(cacheKey, value.toJson().serialize());
  }
}

@riverpod
Future<WalletPortfolioAstrox> fetchRawWalletPortfolioAstrox(
  Ref ref, {
  required Network? network,
  required String? address,
  bool writeToCache = true,
}) async {
  ref.watch(balanceDiffProvider);

  if (address == null || address.isEmpty || network == null) {
    return WalletPortfolioAstrox.empty;
  }

  final nativeSymbol = network.nativeCurrency.symbol.toUpperCase();
  final chainManager = ChainManager.instance;
  final tokens = await ref.watch(filteredTokensProvider.future);

  final balances = await Future.wait(
    tokens.map((token) async {
      final symbol = token.symbol.toUpperCase();

      late final BigInt balanceRaw;
      late final int decimals;
      late final Decimal? price;
      await Future.wait([
        _tokenBalanceMutex
            .withLock(() {
              if (nativeSymbol == symbol) {
                return chainManager.getNativeBalance(address);
              } else {
                return chainManager.getTokenBalance(token.contractAddress, address);
              }
            })
            .then((r) => (balanceRaw, decimals) = r),
        ref
            .watch(
              _getTokenPriceProvider(
                chainName: network.network,
                symbol: token.symbol,
                ucid: token.coinMarketCapUcid,
              ).future,
            )
            .then((r) => price = r),
      ]);

      final balance = Decimal.fromBigInt(balanceRaw).shift(-decimals);
      final tokenBalance = AstroxTokenBalance(
        token: token,
        balance: balance,
        price: price,
      );

      return AstroxToken(
        chainName: network.network,
        symbol: token.symbol,
        name: token.name,
        logo: token.icon,
        address: token.contractAddress,
        decimals: decimals,
        balance: tokenBalance.balance,
        realBalance: tokenBalance.balance,
        priceUsd: tokenBalance.price,
        valueUsd: switch (tokenBalance.price) {
          final price? => tokenBalance.balance * price,
          _ => null,
        },
      );
    }),
  );
  final summary = AstroxWalletSummary(
    totalValueUsd: balances.fold<Decimal>(
      Decimal.zero,
      (sum, token) => sum + (token.valueUsd ?? Decimal.zero),
    ),
    hasEmptyValue: balances.any((e) => e.valueUsd == null),
  );
  final result = WalletPortfolioAstrox(
    summary: summary,
    tokens: balances,
  );
  if (writeToCache) {
    await writeWalletPortfolioAstroxCache(network, address, result);
  }
  return result;
}

@riverpod
Stream<WalletPortfolio> fetchWalletPortfolio(
  Ref ref,
) async* {
  ref.watch(balanceDiffProvider);

  final config = ref.watch(configProvider);
  final network = ref.watch(networkProvider);
  final address = ref.watch(walletAddressProvider);

  if (address == null || address.isEmpty) {
    yield WalletPortfolioOKX2.empty;
    return;
  }

  if (config.useOkxApi && network.chainIndexOKX.isNotEmpty) {
    try {
      const cachePrefix = _walletPortfolioCachePrefixOKX;
      final cacheKey = '$cachePrefix:$address:${network.chainIndexOKX}';
      final cacheData = BoxCaches.get(cacheKey) as String?;
      if (cacheData != null) {
        final cache = WalletPortfolioOKX2.fromJson(cacheData.deserialize());
        yield cache;
      }
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
    }
    final result = await ref.watch(
      fetchRawWalletPortfolioOKXProvider(network: network, address: address).future,
    );
    yield result;
    return;
  }

  try {
    const cachePrefix = _walletPortfolioCachePrefixAstroX;
    final cacheKey = '$cachePrefix:$address:${network.network}';
    final cacheData = BoxCaches.get(cacheKey) as String?;

    if (cacheData != null) {
      final cache = WalletPortfolioAstrox.fromJson(cacheData.deserialize());
      yield cache;
    }
  } catch (e, s) {
    handleExceptions(error: e, stackTrace: s);
  }
  final result = await ref.watch(
    fetchRawWalletPortfolioAstroxProvider(network: network, address: address).future,
  );
  yield result;
}

@riverpod
Decimal? fetchSOLBalanceFromPortfolioOKX(Ref ref) {
  final network = ref.watch(networkProvider);
  if (network.name != 'solana') {
    return null;
  }

  final portfolio = ref.watch(fetchWalletPortfolioProvider).valueOrNull;
  if (portfolio == null) {
    return null;
  }

  final balance = portfolio.tokenByAddress(tokenSOLAddress)?.realBalance;
  return balance ?? Decimal.zero;
}

final tokenBalancePatch = <(Network, String), TokenAmount>{};

@riverpod
Stream<TokenAmount?> tokenBalanceStream(
  Ref ref, {
  required Network? network,
  required String? address,
}) async* {
  if (network == null || address == null) {
    yield null;
    return;
  }

  // Solana only.
  if (network.name != 'solana') {
    yield null;
    return;
  }

  final walletAddress = ref.watch(walletAddressProvider);
  if (walletAddress == null) {
    yield null;
    return;
  }

  // Handle only base58 address.
  if (!sol.Ed25519HDPublicKey.isValidFromBase58(address)) {
    yield null;
    return;
  }

  bool end = false;
  ref.onDispose(() {
    end = true;
  });

  final owner = sol.Ed25519HDPublicKey.fromBase58(walletAddress);
  final mint = sol.Ed25519HDPublicKey.fromBase58(address);

  TokenAmount? lastAmount, lastPatch;

  final rpc = ref.watch(sol.solanaClientProvider);
  int rateLimitDelaySeconds = 1;
  while (!end) {
    final patch = tokenBalancePatch[(network, address)];
    if (patch != lastPatch) {
      lastPatch = patch;
      yield patch;
    }
    try {
      TokenAmount? amount;
      try {
        if (address == tokenSOLAddress) {
          final result = await rpc.rpcClient.getBalance(walletAddress);
          amount = TokenAmount(
            amount: Decimal.fromInt(result.value),
            decimals: sol.solDecimalPlaces,
          );
        } else {
          final result = await rpc.getTokenBalance(owner: owner, mint: mint);
          amount = TokenAmount(
            amount: Decimal.parse(result.amount),
            decimals: result.decimals,
          );
        }
        // Reset the rate limited delay.
        rateLimitDelaySeconds = 1;
      } on sol.JsonRpcException catch (e) {
        if (e.message.contains('could not find account')) {
          amount = switch (lastAmount) {
            final last? => TokenAmount(
              amount: Decimal.zero,
              decimals: last.decimals,
            ),
            _ => null,
          };
        } else {
          rethrow;
        }
      } on sol.HttpException catch (e) {
        // Put extra 2 seconds delay for the next request.
        if (e.toString().contains(io.HttpStatus.tooManyRequests.toString())) {
          rateLimitDelaySeconds += 2;
          LogUtil.i(
            'Rate limited, wait $rateLimitDelaySeconds seconds before next.',
            tag: 'tokenBalanceStream',
          );
        } else {
          rethrow;
        }
      }
      if (amount != null && lastAmount?.amount != amount.amount) {
        tokenBalancePatch.remove((network, address));
        lastAmount = amount;
        LogUtil.d(
          '$address balance: ${amount.realBalance}',
          tag: 'tokenBalanceStream',
        );
        yield amount;
      }
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
    }
    await Future.delayed(Duration(seconds: rateLimitDelaySeconds));
  }
  LogUtil.i('$address balance end', tag: 'tokenBalanceStream');
}

const String _tokenCacheKeyPrefix = 'tokens_cache:v1';

@riverpod
Future<AstroxPaged<AstroxTokenConfig>> fetchTokens(
  Ref ref, {
  required int pageNum,
  required int pageSize,
  required String blockChain,
}) async {
  final ct = ref.cancelToken();
  ref.onDispose(() {
    ct.cancel();
  });

  final result = await ref
      .read(apiAstroxProvider)
      .op
      .getTokens(
        pageNum: pageNum,
        pageSize: pageSize,
        blockChain: blockChain,
        cancelToken: ct,
      );
  return result;
}

// 原始Token数据Provider - 只负责获取和缓存，不进行过滤
@riverpod
Stream<List<AstroxTokenConfig>> fetchRawTokens(Ref ref) async* {
  ref.watch(balanceDiffProvider);
  final network = ref.watch(networkProvider);
  final chainId = network.chainIdEvm.toString();

  try {
    final cacheKey = '$_tokenCacheKeyPrefix:$chainId';
    final cachedData = BoxCaches.get(cacheKey) as List?;

    if (cachedData != null && cachedData.isNotEmpty) {
      final tokens = cachedData.cast<Map>().map((e) => AstroxTokenConfig.fromJson(e.asJson())).toList();
      yield tokens;
    }
  } catch (e, s) {
    handleExceptions(error: e, stackTrace: s);
  }

  final tokensResponse = await ref.watch(
    fetchTokensProvider(
      pageNum: 1,
      pageSize: 10000,
      blockChain: network.network,
    ).future,
  );

  final nativeSymbol = network.nativeCurrency.symbol.toUpperCase();
  final tokens = tokensResponse.list.map((e) {
    if (e.symbol.toUpperCase() == nativeSymbol) {
      return e.copyWith(weight: 0);
    }
    return e;
  }).toList();
  await _cacheTokens(tokens, chainId);
  yield tokens;
}

// 过滤后的token列表Provider - 只负责过滤逻辑
final filteredTokensProvider = FutureProvider.autoDispose<List<AstroxTokenConfig>>((ref) async {
  ref.watch(balanceDiffProvider);
  final network = ref.watch(networkProvider);
  final chainId = network.chainIdEvm.toString();
  final config = ref.watch(configProvider);
  final chainTokenSymbolFilters = config.chainTokenSymbolFilters;
  final rawTokens = await ref.watch(fetchRawTokensProvider.future);
  return _filterTokensByChainId(rawTokens, chainId, chainTokenSymbolFilters);
});

// 过滤token的辅助函数
List<AstroxTokenConfig> _filterTokensByChainId(
  List<AstroxTokenConfig> tokens,
  String chainId,
  Map<String, List<String>>? chainTokenSymbolsFilters,
) {
  List<AstroxTokenConfig> filteredTokens;

  if (chainTokenSymbolsFilters?[chainId] case final symbols?) {
    filteredTokens = tokens.where((token) => symbols.contains(token.symbol)).toList();
  } else {
    filteredTokens = tokens.toList();
  }
  filteredTokens.sort((a, b) => a.weight.compareTo(b.weight));

  return filteredTokens;
}

Future<void> _cacheTokens(
  List<AstroxTokenConfig> tokens,
  String chainId,
) async {
  try {
    final cacheKey = '$_tokenCacheKeyPrefix:$chainId';
    final cacheData = tokens.map((token) => token.toJson()).toList();
    await BoxCaches.put(cacheKey, cacheData);
  } catch (e, s) {
    handleExceptions(error: e, stackTrace: s);
  }
}

final _tokenPriceMutex = MutexQueue(2, tag: 'TokenPriceMutex');

@riverpod
Future<Decimal?> _getTokenPrice(
  Ref ref, {
  required String symbol,
  required String chainName,
  required String ucid,
}) async {
  ref.watch(balanceDiffProvider);
  symbol = symbol.toUpperCase();

  final ct = ref.cancelToken();
  ref.onDispose(() {
    ct.cancel();
  });

  try {
    final ticker = await _tokenPriceMutex.withLock(() {
      return ref
          .read(apiServiceProvider)
          .getTokenPrices(
            symbol: symbol,
            chainName: chainName,
            ucid: ucid,
            cancelToken: ct,
          );
    });
    final price = ticker.price;
    return Decimal.parse('$price');
  } catch (e, s) {
    handleExceptions(error: e, stackTrace: s);
    return null;
  }
}
