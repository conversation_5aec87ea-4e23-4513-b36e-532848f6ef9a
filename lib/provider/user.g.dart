// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchUserInfoHash() => r'd536470c367daf9516d7d788c50be131027bcf9d';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [fetchUserInfo].
@ProviderFor(fetchUserInfo)
const fetchUserInfoProvider = FetchUserInfoFamily();

/// See also [fetchUserInfo].
class FetchUserInfoFamily extends Family<AsyncValue<UserInfo>> {
  /// See also [fetchUserInfo].
  const FetchUserInfoFamily();

  /// See also [fetchUserInfo].
  FetchUserInfoProvider call({String? code}) {
    return FetchUserInfoProvider(code: code);
  }

  @override
  FetchUserInfoProvider getProviderOverride(
    covariant FetchUserInfoProvider provider,
  ) {
    return call(code: provider.code);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchUserInfoProvider';
}

/// See also [fetchUserInfo].
class FetchUserInfoProvider extends AutoDisposeFutureProvider<UserInfo> {
  /// See also [fetchUserInfo].
  FetchUserInfoProvider({String? code})
    : this._internal(
        (ref) => fetchUserInfo(ref as FetchUserInfoRef, code: code),
        from: fetchUserInfoProvider,
        name: r'fetchUserInfoProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$fetchUserInfoHash,
        dependencies: FetchUserInfoFamily._dependencies,
        allTransitiveDependencies:
            FetchUserInfoFamily._allTransitiveDependencies,
        code: code,
      );

  FetchUserInfoProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.code,
  }) : super.internal();

  final String? code;

  @override
  Override overrideWith(
    FutureOr<UserInfo> Function(FetchUserInfoRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchUserInfoProvider._internal(
        (ref) => create(ref as FetchUserInfoRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        code: code,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<UserInfo> createElement() {
    return _FetchUserInfoProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchUserInfoProvider && other.code == code;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, code.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchUserInfoRef on AutoDisposeFutureProviderRef<UserInfo> {
  /// The parameter `code` of this provider.
  String? get code;
}

class _FetchUserInfoProviderElement
    extends AutoDisposeFutureProviderElement<UserInfo>
    with FetchUserInfoRef {
  _FetchUserInfoProviderElement(super.provider);

  @override
  String? get code => (origin as FetchUserInfoProvider).code;
}

String _$fetchUserRelationHash() => r'936667e454f0f5bf16feb39c5c25892fe66bf898';

/// See also [fetchUserRelation].
@ProviderFor(fetchUserRelation)
const fetchUserRelationProvider = FetchUserRelationFamily();

/// See also [fetchUserRelation].
class FetchUserRelationFamily
    extends Family<AsyncValue<(UserRelation relation, String referralCode)?>> {
  /// See also [fetchUserRelation].
  const FetchUserRelationFamily();

  /// See also [fetchUserRelation].
  FetchUserRelationProvider call({required String? code}) {
    return FetchUserRelationProvider(code: code);
  }

  @override
  FetchUserRelationProvider getProviderOverride(
    covariant FetchUserRelationProvider provider,
  ) {
    return call(code: provider.code);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchUserRelationProvider';
}

/// See also [fetchUserRelation].
class FetchUserRelationProvider
    extends
        AutoDisposeFutureProvider<
          (UserRelation relation, String referralCode)?
        > {
  /// See also [fetchUserRelation].
  FetchUserRelationProvider({required String? code})
    : this._internal(
        (ref) => fetchUserRelation(ref as FetchUserRelationRef, code: code),
        from: fetchUserRelationProvider,
        name: r'fetchUserRelationProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$fetchUserRelationHash,
        dependencies: FetchUserRelationFamily._dependencies,
        allTransitiveDependencies:
            FetchUserRelationFamily._allTransitiveDependencies,
        code: code,
      );

  FetchUserRelationProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.code,
  }) : super.internal();

  final String? code;

  @override
  Override overrideWith(
    FutureOr<(UserRelation relation, String referralCode)?> Function(
      FetchUserRelationRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchUserRelationProvider._internal(
        (ref) => create(ref as FetchUserRelationRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        code: code,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<
    (UserRelation relation, String referralCode)?
  >
  createElement() {
    return _FetchUserRelationProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchUserRelationProvider && other.code == code;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, code.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchUserRelationRef
    on
        AutoDisposeFutureProviderRef<
          (UserRelation relation, String referralCode)?
        > {
  /// The parameter `code` of this provider.
  String? get code;
}

class _FetchUserRelationProviderElement
    extends
        AutoDisposeFutureProviderElement<
          (UserRelation relation, String referralCode)?
        >
    with FetchUserRelationRef {
  _FetchUserRelationProviderElement(super.provider);

  @override
  String? get code => (origin as FetchUserRelationProvider).code;
}

String _$fetchUsersFromRelationHash() =>
    r'dab15e971416c03687775d41a1dc22d4b73d400e';

/// See also [fetchUsersFromRelation].
@ProviderFor(fetchUsersFromRelation)
const fetchUsersFromRelationProvider = FetchUsersFromRelationFamily();

/// See also [fetchUsersFromRelation].
class FetchUsersFromRelationFamily
    extends Family<AsyncValue<Paged<UserFromRelation>>> {
  /// See also [fetchUsersFromRelation].
  const FetchUsersFromRelationFamily();

  /// See also [fetchUsersFromRelation].
  FetchUsersFromRelationProvider call({
    required UserFromRelationType type,
    required int page,
  }) {
    return FetchUsersFromRelationProvider(type: type, page: page);
  }

  @override
  FetchUsersFromRelationProvider getProviderOverride(
    covariant FetchUsersFromRelationProvider provider,
  ) {
    return call(type: provider.type, page: provider.page);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchUsersFromRelationProvider';
}

/// See also [fetchUsersFromRelation].
class FetchUsersFromRelationProvider
    extends AutoDisposeFutureProvider<Paged<UserFromRelation>> {
  /// See also [fetchUsersFromRelation].
  FetchUsersFromRelationProvider({
    required UserFromRelationType type,
    required int page,
  }) : this._internal(
         (ref) => fetchUsersFromRelation(
           ref as FetchUsersFromRelationRef,
           type: type,
           page: page,
         ),
         from: fetchUsersFromRelationProvider,
         name: r'fetchUsersFromRelationProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$fetchUsersFromRelationHash,
         dependencies: FetchUsersFromRelationFamily._dependencies,
         allTransitiveDependencies:
             FetchUsersFromRelationFamily._allTransitiveDependencies,
         type: type,
         page: page,
       );

  FetchUsersFromRelationProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.type,
    required this.page,
  }) : super.internal();

  final UserFromRelationType type;
  final int page;

  @override
  Override overrideWith(
    FutureOr<Paged<UserFromRelation>> Function(
      FetchUsersFromRelationRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchUsersFromRelationProvider._internal(
        (ref) => create(ref as FetchUsersFromRelationRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        type: type,
        page: page,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Paged<UserFromRelation>> createElement() {
    return _FetchUsersFromRelationProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchUsersFromRelationProvider &&
        other.type == type &&
        other.page == page;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, type.hashCode);
    hash = _SystemHash.combine(hash, page.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchUsersFromRelationRef
    on AutoDisposeFutureProviderRef<Paged<UserFromRelation>> {
  /// The parameter `type` of this provider.
  UserFromRelationType get type;

  /// The parameter `page` of this provider.
  int get page;
}

class _FetchUsersFromRelationProviderElement
    extends AutoDisposeFutureProviderElement<Paged<UserFromRelation>>
    with FetchUsersFromRelationRef {
  _FetchUsersFromRelationProviderElement(super.provider);

  @override
  UserFromRelationType get type =>
      (origin as FetchUsersFromRelationProvider).type;
  @override
  int get page => (origin as FetchUsersFromRelationProvider).page;
}

String _$persistentTokenRepoHash() =>
    r'cce8ed4fe540122266cbba8ffdf3dbbdaf0eb182';

/// See also [PersistentTokenRepo].
@ProviderFor(PersistentTokenRepo)
final persistentTokenRepoProvider =
    AutoDisposeNotifierProvider<PersistentTokenRepo, String?>.internal(
      PersistentTokenRepo.new,
      name: r'persistentTokenRepoProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$persistentTokenRepoHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$PersistentTokenRepo = AutoDisposeNotifier<String?>;
String _$userRepoHash() => r'71e186bdc0063401dffc8a6b169d414ad9637b13';

/// See also [UserRepo].
@ProviderFor(UserRepo)
final userRepoProvider =
    AutoDisposeNotifierProvider<UserRepo, UserInfo?>.internal(
      UserRepo.new,
      name: r'userRepoProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$userRepoHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$UserRepo = AutoDisposeNotifier<UserInfo?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
