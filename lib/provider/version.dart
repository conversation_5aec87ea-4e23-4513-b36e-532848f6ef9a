import 'package:flutter/foundation.dart';

import 'package:in_app_update/in_app_update.dart';
import 'package:me_models/me_models.dart';
import 'package:me_ui/me_ui.dart' show UpgradeDialog;
import 'package:me_utils/me_utils.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '/constants/envs.dart' as envs;
import '/constants/release.dart';
import '/internals/methods.dart';
import 'api.dart';

final AppVersion _currentVersion = AppVersion.fallback(
  currentName: PackageUtil.versionName,
  currentCode: PackageUtil.versionCode,
);

final appVersionProvider = FutureProvider<(bool, AppVersion)>((ref) async {
  if (kDebugMode) {
    LogUtil.w(
      'Overriding app version with current.',
      tag: 'appVersionProvider',
      tagWithTrace: false,
    );
    return (true, _currentVersion);
  }
  try {
    final res = await ref
        .read(httpProvider)
        .post(
          '${envs.envApiUrlAstroxOp}/version/latestUpdate',
          data: <String, dynamic>{
            'channel': Release.channel,
            'packageName': PackageUtil.packageName,
            'versionCode': PackageUtil.versionCode,
          },
        );
    final version = AppVersion.fromJson(res.data);
    LogUtil.i(
      'Remote version: $version',
      tag: 'appVersionProvider',
      tagWithTrace: false,
    );
    envs.isAuditing = version.isAuditing(currentCode: PackageUtil.versionCode);
    return (true, version);
  } on HttpException catch (e) {
    // No versions can be found on the current channel, so we fallback to the
    // current version instead of throws.
    if (e.response?.statusCode == 404) {
      LogUtil.w(
        'Current version does not exist, '
        'fallback to use the current version.',
        tag: 'appVersionProvider',
        tagWithTrace: false,
      );
    }
    envs.isAuditing = false;
    return (false, _currentVersion);
  } catch (e, s) {
    handleExceptions(
      error: e,
      stackTrace: s,
      tag: 'appVersionProvider',
      tagWithTrace: false,
    );
    envs.isAuditing = false;
    return (false, _currentVersion);
  }
});

Future<void> checkAppUpdate(AppVersion version) async {
  if (!version.shouldUpdate(currentCode: PackageUtil.versionCode)) {
    return;
  }
  if (version.forceUpdate) {
    await UpgradeDialog.show(version);
    return;
  }
  if (!Release.channel.endsWith('-play')) {
    await UpgradeDialog.show(version);
    return;
  }
  // Check for Google Play updates.
  final v = await InAppUpdate.checkForUpdate().then(
    (v) {
      return (
        availability: v.updateAvailability == UpdateAvailability.updateAvailable,
        immediateUpdateAllowed: v.immediateUpdateAllowed,
        flexibleUpdateAllowed: v.flexibleUpdateAllowed,
      );
    },
    onError: (e, s) {
      LogUtil.e(e, stackTrace: s, report: false);
      return (
        availability: false,
        immediateUpdateAllowed: false,
        flexibleUpdateAllowed: false,
      );
    },
  );
  if (!v.availability) {
    await UpgradeDialog.show(version);
    return;
  }
  try {
    if (v.immediateUpdateAllowed) {
      await InAppUpdate.performImmediateUpdate();
    } else if (v.flexibleUpdateAllowed) {
      final r = await InAppUpdate.startFlexibleUpdate();
      if (r == AppUpdateResult.inAppUpdateFailed) {
        await UpgradeDialog.show(version);
      } else if (r == AppUpdateResult.success) {
        await InAppUpdate.completeFlexibleUpdate();
      }
    }
    return;
  } catch (e, s) {
    handleExceptions(
      error: e,
      stackTrace: s,
      tag: 'checkAppUpdate',
      tagWithTrace: false,
    );
    await UpgradeDialog.show(version);
  }
}
