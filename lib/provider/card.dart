import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '/api/front_end.dart';
import '/internals/box.dart' show Boxes;
import '/models/card.dart';
import '/models/user.dart' show ProfileMode, UserInfo;
import '/provider/api.dart';
import 'business.dart' show configProvider;
import 'user.dart' show userRepoProvider;

part 'card.g.dart';

@riverpod
Future<List<CardInfo>> fetchMyCards(
  Ref ref,
) {
  return ref.read(apiServiceProvider).getMyCards();
}

@riverpod
Future<CardInfo> fetchPublicCard(
  Ref ref, {
  required String code,
}) {
  return ref.read(apiServiceProvider).getCard(cardCode: code);
}

@riverpod
Future<UserInfo> fetchPublicProfile(
  Ref ref, {
  required String code,
}) {
  return ref.read(apiServiceProvider).getPublicProfile(code: code);
}

@riverpod
Future<List<Social>> fetchSocials(
  Ref ref, {
  String? code,
}) {
  final ct = ref.cancelToken();
  ref.onDispose(() {
    ct.cancel();
  });
  if (code == null) {
    return ref.read(apiServiceProvider).socialQuery(cancelToken: ct);
  } else {
    return ref.read(apiServiceProvider).getPublicSocials(code: code, cancelToken: ct);
  }
}

@riverpod
Future<List<String>> fetchEthccTopics(
  Ref ref,
) {
  return ref.read(apiServiceProvider).getEthccTopicsAll();
}

@riverpod
Future<List<String>> fetchEthccRoles(
  Ref ref,
) {
  return ref.read(apiServiceProvider).getEthccRolesAll();
}

@riverpod
Future<EthccProfile?> fetchEthccProfile(
  Ref ref, {
  String? code,
}) {
  if (code != null) {
    return ref.read(apiServiceProvider).getEthccPublicProfile(code: code);
  } else {
    return ref.read(apiServiceProvider).getEthccProfile();
  }
}

@riverpod
Future<FrontEndGitHubContributionCollection?> fetchGitHubContributions(
  Ref ref, {
  required String handle,
}) {
  return ref.read(apiFrontEndProvider).getGitHubContributions(handle: handle);
}

@riverpod
Future<bool> validateETHCCProfile(
  Ref ref, {
  required bool validateProfile,
}) async {
  final user = ref.watch(userRepoProvider);
  final cards = await ref.read(fetchMyCardsProvider.future);
  if (user == null) {
    return false;
  }

  return ref.watch(
    _validateETHCCProfileInnerProvider(
      eventIds: cards.map((c) => c.eventId).join(','),
      userInfo: user,
      validateProfile: validateProfile,
    ),
  );
}

@riverpod
Future<bool> validateETHCCProfileByCode(
  Ref ref, {
  required String code,
}) async {
  late final CardInfo card;
  late final UserInfo user;
  await Future.wait([
    ref.read(fetchPublicCardProvider(code: code).future).then((value) => card = value),
    ref.read(fetchPublicProfileProvider(code: code).future).then((value) => user = value),
  ]);
  final result = ref.read(
    _validateETHCCProfileInnerProvider(
      // ignore: provider_parameters
      eventIds: card.eventId.toString(),
      userInfo: user,
      validateProfile: true,
    ),
  );
  return result;
}

@riverpod
bool _validateETHCCProfileInner(
  Ref ref, {
  required String eventIds,
  required UserInfo userInfo,
  required bool validateProfile,
}) {
  final config = ref.watch(configProvider);
  final configEvents = Set<int>.from(config.ethccEventIds);
  if (configEvents.isEmpty) {
    return false;
  }

  final cardEvents = eventIds.split(',').map(int.parse);
  final hasEventMatch = configEvents.contains(0) || cardEvents.any(configEvents.contains);

  // 如果不需要验证 profile，直接返回事件匹配结果
  if (!validateProfile) {
    return hasEventMatch;
  }

  // **核心逻辑2: 确定要验证的 Profile**
  final ProfileMode profileToCheck;
  if (userInfo.profileMode == ProfileMode.EMPTY) {
    profileToCheck = config.defaultMode;
  } else {
    profileToCheck = userInfo.profileMode;
  }

  return hasEventMatch && (profileToCheck == ProfileMode.ETHCC);
}

@riverpod
bool? _isActiveGuide(Ref ref) {
  final config = ref.watch(configProvider);
  final cards = ref.watch(fetchMyCardsProvider).valueOrNull ?? [];
  // 1. 如果config.activationGuide不为true直接返回false
  if (!config.activationGuide) {
    return false;
  }

  // 2. 过滤掉虚拟卡，获取实体卡
  final realCards = cards.where((card) => !card.virtualCard).toList();

  // 4. 对于为空的（没有实体卡），都返回true
  if (realCards.isEmpty) {
    return true;
  }

  // 3. 对于不为空的（有实体卡），每5分钟内最多显示一次返回true
  const String lastShownKey = 'activation_guide_last_shown';
  const int intervalMinutes = 60 * 24;
  const int intervalMilliseconds = intervalMinutes * 60 * 1000;

  try {
    final now = DateTime.now().millisecondsSinceEpoch;
    final lastShown = Boxes.settings.get(lastShownKey) as int?;

    if (lastShown == null || (now - lastShown) >= intervalMilliseconds) {
      // 记录本次显示时间
      Boxes.settings.put(lastShownKey, now);
      return true;
    }

    // 在5分钟间隔内，不显示
    return false;
  } catch (e) {
    // 如果读取/写入失败，默认显示（安全回退）
    return true;
  }
}

// 创建公开的provider供UI使用
@riverpod
bool? watchIsActiveGuide(Ref ref) {
  return ref.watch(_isActiveGuideProvider);
}
