import 'dart:async' show FutureOr;

import 'package:privy_flutter/privy_flutter.dart' as privy show PrivyUser;

// ignore: implementation_imports
import 'package:privy_flutter/src/models/embedded_wallet/embedded_wallet.dart' show EmbeddedWallet;

import '/models/business.dart' show Network;

export 'package:privy_flutter/privy_flutter.dart' show PrivyUser;

export '/models/business.dart' show Network;

abstract interface class ChainService<W extends EmbeddedWallet> {
  String? get walletAddress;

  privy.PrivyUser? get user;

  void initialize({
    required Network network,
    required privy.PrivyUser user,
  });

  FutureOr<void> dispose();

  void updatePrivyUser(privy.PrivyUser user);

  Future<BigInt> getNativeTokenBalance(String walletAddress);

  Future<(BigInt balance, int decimals)> getTokenBalance(
    String tokenAddress,
    String walletAddress,
  );

  W getWallet();

  Future<String> sendNativeToken(
    String to,
    BigInt value, {
    BigInt? gasLimit,
  });

  Future<String> sendToken(
    String tokenAddress,
    String to,
    BigInt value, {
    BigInt? gasLimit,
  });
}
