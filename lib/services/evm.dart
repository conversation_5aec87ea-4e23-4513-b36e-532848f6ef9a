import 'dart:async';
import 'dart:convert' show jsonEncode;
import 'dart:typed_data' show Uint8List;

import 'package:http/http.dart' as http;
import 'package:me_utils/me_utils.dart';
import 'package:privy_flutter/privy_flutter.dart' as privy show EmbeddedEthereumWallet, EthereumRpcRequest, PrivyUser;
import 'package:uuid/uuid.dart';
import 'package:wallet/wallet.dart' as web3w;
import 'package:web3dart/json_rpc.dart' as web3j;
import 'package:web3dart/web3dart.dart' as web3;

import '/feat/chain/evm/abi/erc20.g.dart' as erc20;
import '/internals/methods.dart' show handleExceptions;
import 'service.dart';

final class _JsonRpc extends web3j.JsonRPC {
  _JsonRpc(super.url, super.client);

  static const _tag = 'web3j.JsonRPC';

  @override
  Future<web3j.RPCResponse> call(String function, [List<dynamic>? params]) async {
    final uuid = const Uuid().v8();
    LogUtil.d(
      '===> $uuid\n'
      '$function, $params',
      tag: _tag,
      tagWithTrace: false,
    );
    final response = await super.call(function, params);
    LogUtil.d(
      '<=== $uuid\n'
      '$function, $params',
      tag: _tag,
      tagWithTrace: false,
    );
    return response;
  }
}

class EvmService implements ChainService<privy.EmbeddedEthereumWallet> {
  static const _tag = '💸 SolanaService';

  web3.Web3Client get client => _client!;
  web3.Web3Client? _client;

  Network? _network;

  @override
  String? get walletAddress => _walletAddress;
  String? _walletAddress;

  @override
  privy.PrivyUser? get user => _user;
  privy.PrivyUser? _user;

  @override
  void initialize({
    required Network network,
    required privy.PrivyUser user,
  }) {
    _client?.dispose();
    _client = null;

    final rpcUrl = network.rpcProviders.first.url;
    _client = web3.Web3Client.custom(_JsonRpc(rpcUrl, http.Client()));
    _network = network;
    _walletAddress = user.embeddedEthereumWallets.firstOrNull?.address;
    _user = user;

    LogUtil.d(
      'EvmService Initialized: rpcUrl=$rpcUrl, walletAddress=$_walletAddress, chainId=${network.chainIdEvm}',
      tag: _tag,
      tagWithTrace: false,
    );
  }

  @override
  void updatePrivyUser(privy.PrivyUser user) {
    if (_network case final network?) {
      initialize(network: network, user: user);
    }
  }

  @override
  Future<void> dispose() async {
    final client = _client;
    _client = null;
    await client?.dispose();
  }

  @override
  Future<BigInt> getNativeTokenBalance(String walletAddress) async {
    final network = _network!;
    final nativeCurrency = network.nativeCurrency;
    LogUtil.d(
      'Getting ${network.network}(${network.chainIdEvm}) native token balance:'
      '\n|-- walletAddress=$walletAddress'
      '\n|-- tokenName=${nativeCurrency.name}'
      '\n|-- tokenSymbol=${nativeCurrency.symbol}',
      tag: _tag,
      tagWithTrace: false,
    );

    try {
      final balance = await client.getBalance(
        web3w.EthereumAddress.fromHex(walletAddress),
      );
      return balance.getInWei;
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      return BigInt.zero;
    }
  }

  @override
  Future<(BigInt, int)> getTokenBalance(
    String tokenAddress,
    String walletAddress,
  ) async {
    final network = _network!;
    LogUtil.d(
      'Getting ${network.network}(${network.chainIdEvm}) token balance:'
      '\n|-- walletAddress=$walletAddress'
      '\n|-- tokenAddress=$tokenAddress',
      tag: _tag,
      tagWithTrace: false,
    );

    final contract = erc20.Erc20(
      address: web3w.EthereumAddress.fromHex(tokenAddress),
      client: client,
    );

    late final BigInt balance;
    late final int decimals;
    await Future.wait([
      contract.balanceOf((owner: web3w.EthereumAddress.fromHex(walletAddress))).then((r) => balance = r),
      contract.decimals().then((r) => decimals = r.toInt()),
    ]);

    return (balance, decimals);
  }

  Future<Map<String, dynamic>> _buildTransactionParams({
    required Network network,
    required String to,
    required BigInt nativeValue,
    BigInt? gasLimit,
    String? data,
  }) async {
    final from = _walletAddress!;

    // 1. 获取 Nonce
    final nonce = await client.getTransactionCount(
      web3w.EthereumAddress.fromHex(from),
    );
    final String nonceHex = '0x${nonce.toRadixString(16)}';

    // 2. 估算 Gas Limit，如果没有传入 gasLimit，或者传入的 gasLimit 不合理，则进行估算
    BigInt estimatedGasLimit = await client.estimateGas(
      sender: web3w.EthereumAddress.fromHex(from),
      to: web3w.EthereumAddress.fromHex(to),
      value: web3w.EtherAmount.inWei(nativeValue),
      data: data != null && data.isNotEmpty ? _hexToBytes(data.replaceFirst(RegExp(r'^0x'), '')) : null,
    );
    // 可以在估算值上稍微增加一点，例如 10-20%
    estimatedGasLimit = (estimatedGasLimit * BigInt.from(110)) ~/ BigInt.from(100);
    LogUtil.d('Estimated gas limit: ${estimatedGasLimit.toString()}', tag: _tag, tagWithTrace: false);

    final String finalGasLimitHex = '0x${estimatedGasLimit.toRadixString(16)}';

    // 3. 判断是否支持 EIP-1559 并获取 Gas 价格/费用
    BigInt? maxFeePerGas;
    BigInt? maxPriorityFeePerGas;

    bool isEIP1559Supported = false; // 默认不启用 EIP-1559

    try {
      final block = await client.getBlockInformation(blockNumber: 'latest');
      if (block.baseFeePerGas case final baseFeePerGas?) {
        isEIP1559Supported = true;
        final BigInt baseFee = baseFeePerGas.getInWei;
        LogUtil.d('EIP-1559 supported. Base Fee: ${baseFee.toString()} Wei', tag: _tag, tagWithTrace: false);

        try {
          final priorityFeeResponse = await client.makeRPCCall('eth_maxPriorityFeePerGas', []);
          if (priorityFeeResponse is String && priorityFeeResponse.startsWith('0x')) {
            maxPriorityFeePerGas = BigInt.parse(priorityFeeResponse.substring(2), radix: 16);
            LogUtil.d(
              'Fetched maxPriorityFeePerGas from RPC: ${maxPriorityFeePerGas.toString()} Wei',
              tag: _tag,
              tagWithTrace: false,
            );
          } else if (priorityFeeResponse is int) {
            maxPriorityFeePerGas = BigInt.from(priorityFeeResponse);
            LogUtil.d(
              'Fetched maxPriorityFeePerGas from RPC: ${maxPriorityFeePerGas.toString()} Wei',
              tag: _tag,
              tagWithTrace: false,
            );
          } else {
            maxPriorityFeePerGas = BigInt.from(100000000); // 0.1 Gwei
            LogUtil.d(
              'Warning: Unexpected response for eth_maxPriorityFeePerGas on Arbitrum. Using default 0.1 Gwei.',
              tag: _tag,
              tagWithTrace: false,
            );
          }
        } catch (e, s) {
          LogUtil.e(e, tag: _tag, stackTrace: s);
          maxPriorityFeePerGas = BigInt.from(100000000); // 0.1 Gwei
        }

        // maxFeePerGas 的计算
        maxFeePerGas = (baseFee * BigInt.from(110) ~/ BigInt.from(100)) + maxPriorityFeePerGas;
        LogUtil.d('Calculated maxFeePerGas: ${maxFeePerGas.toString()} Wei', tag: _tag, tagWithTrace: false);
      } else {
        isEIP1559Supported = false;
        LogUtil.d(
          'EIP-1559 not supported or baseFeePerGas not found. Falling back to legacy transaction.',
          tag: _tag,
          tagWithTrace: false,
        );
      }
    } catch (e, s) {
      LogUtil.e(e, tag: _tag, stackTrace: s);
      isEIP1559Supported = false;
    }

    final txParams = <String, dynamic>{
      'from': from,
      'to': to,
      'value': '0x${nativeValue.toRadixString(16)}',
      'chainId': '0x${network.chainIdEvm.toRadixString(16)}',
      'nonce': nonceHex,
      if (data != null && data.isNotEmpty) 'data': data,
    };

    if (isEIP1559Supported) {
      txParams.addAll({
        'gas': finalGasLimitHex,
        'maxFeePerGas': '0x${maxFeePerGas!.toRadixString(16)}',
        'maxPriorityFeePerGas': '0x${maxPriorityFeePerGas!.toRadixString(16)}',
      });
      LogUtil.d('Transaction params (EIP-1559): $txParams', tag: _tag, tagWithTrace: false);
    } else {
      // 传统方式
      BigInt gasPrice;
      try {
        final price = await client.getGasPrice();
        gasPrice = price.getInWei * BigInt.from(120) ~/ BigInt.from(100);
        LogUtil.d(
          'Fetched gasPrice: ${price.getInWei.toString()} Wei. Adjusted gasPrice: ${gasPrice.toString()} Wei',
          tag: _tag,
          tagWithTrace: false,
        );
      } catch (e, s) {
        LogUtil.e(e, tag: _tag, stackTrace: s);
        gasPrice = BigInt.from(10000000000); // 10 Gwei
      }

      txParams.addAll({
        'gasLimit': finalGasLimitHex,
        'gasPrice': '0x${gasPrice.toRadixString(16)}',
      });
      LogUtil.d('Transaction params (Legacy): $txParams', tag: _tag, tagWithTrace: false);
    }

    return txParams;
  }

  @override
  privy.EmbeddedEthereumWallet getWallet() {
    final wallet = _user?.embeddedEthereumWallets.firstOrNull;
    if (wallet == null) {
      throw StateError('No Ethereum wallet found');
    }
    return wallet;
  }

  Future<String> _sendRawTransaction(
    String signedTx,
    privy.EmbeddedEthereumWallet wallet,
  ) async {
    if (signedTx.startsWith('0x')) {
      signedTx = signedTx.substring(2);
    }
    final result = await client.sendRawTransaction(_hexToBytes(signedTx));
    if (result.length != 66) {
      throw StateError('Transaction failed: expecting 66 length, got ${result.length}');
    }
    return result;
  }

  @override
  Future<String> sendNativeToken(
    String to,
    BigInt value, {
    BigInt? gasLimit,
  }) async {
    final network = _network!;
    final nativeCurrency = network.nativeCurrency;
    LogUtil.d(
      'Sending ${network.network}(${network.chainIdEvm}) native token:'
      '\n|-- tokenName=${nativeCurrency.name}'
      '\n|-- tokenSymbol=${nativeCurrency.symbol}'
      '\n|-- to=$to'
      '\n|-- value=$value',
      tag: _tag,
      tagWithTrace: false,
    );

    // https://docs.privy.io/wallets/using-wallets/ethereum/sign-a-transaction#flutter
    final txPayload = await _buildTransactionParams(
      network: network,
      to: to,
      nativeValue: value,
      gasLimit: gasLimit,
    );
    final signRequest = privy.EthereumRpcRequest(
      method: 'eth_signTransaction',
      params: [jsonEncode(txPayload)],
    );
    final wallet = getWallet();
    final signResult = await wallet.provider.request(signRequest);

    final completer = Completer<String>();
    signResult.fold(
      onSuccess: (response) async {
        try {
          final txHash = await _sendRawTransaction(response.data, wallet);
          completer.complete(txHash);
        } catch (e, s) {
          completer.completeError(e, s);
        }
      },
      onFailure: (error) {
        handleExceptions(error: error, stackTrace: StackTrace.current);
        completer.completeError(error);
      },
    );

    return completer.future;
  }

  @override
  Future<String> sendToken(
    String tokenAddress,
    String to,
    BigInt value, {
    BigInt? gasLimit,
  }) async {
    final network = _network!;
    LogUtil.d(
      'Sending ${network.network}(${network.chainIdEvm}) token:'
      '\n|-- walletAddress=$walletAddress'
      '\n|-- tokenAddress=$tokenAddress'
      '\n|-- to=$to'
      '\n|-- value=$value',
      tag: _tag,
      tagWithTrace: false,
    );

    final wallet = getWallet();

    final contract = erc20.Erc20(
      address: web3w.EthereumAddress.fromHex(tokenAddress),
      client: client,
    ).self;
    final transferFunction = contract.function('transfer');
    final data = transferFunction.encodeCall([
      web3w.EthereumAddress.fromHex(to),
      value,
    ]);
    final dataHex = '0x${_bytesToHex(data)}';

    // https://docs.privy.io/wallets/using-wallets/ethereum/sign-a-transaction#flutter
    // ERC20 转账不需要发送原生币
    final txPayload = await _buildTransactionParams(
      network: network,
      to: tokenAddress,
      nativeValue: BigInt.zero,
      gasLimit: gasLimit ?? BigInt.from(100000),
      data: dataHex,
    );
    final signRequest = privy.EthereumRpcRequest(
      method: 'eth_signTransaction',
      params: [jsonEncode(txPayload)],
    );
    final signResult = await wallet.provider.request(signRequest);

    final completer = Completer<String>();
    signResult.fold(
      onSuccess: (response) async {
        try {
          final txHash = await _sendRawTransaction(response.data, wallet);
          completer.complete(txHash);
        } catch (e, s) {
          completer.completeError(e, s);
        }
      },
      onFailure: (error) {
        completer.completeError(error);
      },
    );

    return completer.future;
  }

  Uint8List _hexToBytes(String hex) {
    final result = Uint8List(hex.length ~/ 2);
    for (var i = 0; i < hex.length; i += 2) {
      final num = int.parse(hex.substring(i, i + 2), radix: 16);
      result[i ~/ 2] = num;
    }
    return result;
  }

  String _bytesToHex(Uint8List bytes) {
    return bytes.map((byte) => byte.toRadixString(16).padLeft(2, '0')).join();
  }
}
