import 'dart:async';
import 'dart:convert';

import 'package:flutter/widgets.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart' show InAppWebViewController;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:me_bridge/me_bridge.dart';
import 'package:me_misc/me_misc.dart' show meNavigator;
import 'package:me_utils/me_utils.dart';
import 'package:url_launcher/url_launcher.dart' show canLaunchUrl, launchUrl, LaunchMode;

import '/constants/envs.dart' show isSealed;
import '/internals/box.dart' show BoxService;
import '/internals/methods.dart' show handleExceptions;
import '/models/user.dart' show UserInfo;
import '/routes/card3_routes.dart' show routeNames;

import 'launch.dart' show askToLaunchUrlExternally;

String inHouseJSSourceInjectToken(String token) {
  return '''
    // 设置token到全局变量
    window.card3Token = `${Uri.encodeComponent(token)}`;
    
    // 添加全局函数供网页调用
    window.getCardToken = function() {
      return window.card3Token;
    };
    // 触发自定义事件通知网页
    document.dispatchEvent(new CustomEvent('tokenReady', {
      detail: { token: window.card3Token }
    }));''';
}

final defaultInHouseBridgeHostRegExp = RegExp(r'\S*\.?card3\.(ai|co|fun)');

const defaultInHouseBridge = InHouseBridge(
  method: 'bridge',
  subMethods: <JsonRPCMethod>{
    _GetAppVersion(),
    _GetToken(),
    _GetUserInfo(),
    _LaunchAppPage(),
    _LaunchUrl(),
  },
  adapter: CborJsonRPCAdapter(),
);

@immutable
class InHouseBridge implements WebViewBridge {
  const InHouseBridge({
    required this.adapter,
    required this.method,
    required this.subMethods,
  });

  @override
  final String method;
  @override
  final Set<JsonRPCMethod> subMethods;
  @override
  final JsonRPCAdapter<JsonRPCRequest, JsonRPCResponse> adapter;

  @override
  void inject(
    BuildContext context,
    WidgetRef ref,
    InAppWebViewController controller,
  ) {
    final subs = Map<String, JsonRPCMethod>.fromEntries(
      subMethods.map((JsonRPCMethod e) => MapEntry(e.name, e)),
    );
    controller.addJavaScriptHandler(
      handlerName: method,
      callback: (args) async {
        final buf = args.firstOrNull;
        final request = await adapter.decodeRequest(buf);
        final uri = await controller.getUrl();

        bool allowed = false;
        if (uri case final uri?) {
          final hostsMatched = defaultInHouseBridgeHostRegExp.hasMatch(uri.host);
          if (hostsMatched) {
            allowed = true;
          }
        }
        if (!isSealed) {
          allowed = true;
        }

        if (allowed) {
          return handleJsonRPCCall(
            context: context,
            ref: ref,
            bridge: this,
            controller: controller,
            subMethods: subs,
            request: request,
            handleExceptions: (e, s) => handleExceptions(
              error: e,
              stackTrace: s,
              tag: '$runtimeType',
              tagWithTrace: false,
            ),
          );
        }
        return _rejectRPCCall(uri, request);
      },
    );
  }

  Future<String> _rejectRPCCall(Uri? uri, JsonRPCRequest request) async {
    LogUtil.d(
      '===>\n'
      '$runtimeType rejected: '
      'method: $method, '
      'uri: $uri, '
      'req: $request',
    );
    final rep = JsonRPCResponse(
      id: request.id,
      jsonrpc: request.jsonrpc,
      error: JsonRPCError(
        // code: MEError.bridgeIllegalState, // 50002
        code: 50002,
        message: request.method,
      ),
    );
    LogUtil.d(
      '<===\n'
      '$runtimeType rejected: '
      'method: $method, '
      'uri: $uri, '
      'rep: $rep',
    );
    return adapter.encodeResponse(rep);
  }
}

class _GetAppVersion extends JsonRPCMethod {
  const _GetAppVersion() : super('get_app_version');

  @override
  Future<List<Object>> call(
    BuildContext context,
    WidgetRef ref,
    WebViewBridge bridge,
    InAppWebViewController controller,
    JsonRPCRequest request,
  ) async {
    return [PackageUtil.versionName, PackageUtil.versionCode];
  }
}

class _GetToken extends JsonRPCMethod {
  const _GetToken() : super('get_token');

  @override
  Future<String> call(
    BuildContext context,
    WidgetRef ref,
    WebViewBridge bridge,
    InAppWebViewController controller,
    JsonRPCRequest request,
  ) async {
    return BoxService.getToken() ?? '';
  }
}

class _GetUserInfo extends JsonRPCMethod {
  const _GetUserInfo() : super('get_user_info');

  @override
  Future<String> call(
    BuildContext context,
    WidgetRef ref,
    WebViewBridge bridge,
    InAppWebViewController controller,
    JsonRPCRequest request,
  ) async {
    try {
      final userInfo = BoxService.getUserInfo();
      if (userInfo == null) {
        const defaultUserInfo = UserInfo();
        final jsonString = jsonEncode(defaultUserInfo.toJson());
        return jsonString;
      }
      final jsonString = jsonEncode(userInfo.toJson());
      return jsonString;
    } catch (e) {
      const defaultUserInfo = UserInfo();
      final jsonString = jsonEncode(defaultUserInfo.toJson());
      return jsonString;
    }
  }
}

class _LaunchAppPage extends JsonRPCMethod {
  const _LaunchAppPage() : super('launch_app_page');

  @override
  Future<bool> call(
    BuildContext context,
    WidgetRef ref,
    WebViewBridge bridge,
    InAppWebViewController controller,
    JsonRPCRequest request,
  ) async {
    try {
      final String? route = request.params[0];
      if (route == null || route.isEmpty || !routeNames.contains(route)) {
        return false;
      }
      final arguments = request.params.elementAtOrNull(1);
      meNavigator.pushNamed(route, arguments: arguments);
      return true;
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      return false;
    }
  }
}

class _LaunchUrl extends JsonRPCMethod {
  const _LaunchUrl() : super('launch_url');

  @override
  Future<bool> call(
    BuildContext context,
    WidgetRef ref,
    WebViewBridge bridge,
    InAppWebViewController controller,
    JsonRPCRequest request,
  ) async {
    final url = request.params[0] as String?;
    final uri = Uri.tryParse(url ?? '');
    if (uri == null) {
      return false;
    }
    if (!await canLaunchUrl(uri)) {
      return false;
    }
    if (!await askToLaunchUrlExternally()) {
      return false;
    }
    return launchUrl(uri, mode: LaunchMode.externalApplication);
  }
}
