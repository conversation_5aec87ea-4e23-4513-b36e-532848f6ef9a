import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:me_l10n/me_l10n.dart';
import 'package:me_ui/me_ui.dart' hide ScrollableBottomSheet;

import '/constants/constants.dart' show ToastMessages;
import '/feat/card/helper.dart' show CardActivateParams;
import '/internals/methods.dart' show isNetworkError;
import '/models/card.dart' show CardType, CardInfoBasicInactivated;
import '/provider/api.dart' show ApiException, apiServiceProvider;
import '/provider/card.dart' show fetchMyCardsProvider;
import '/res/assets.gen.dart';
import '/ui/widgets/app_loading.dart' show AppLoading;
import '/ui/widgets/dialog/scrollable_bottom_sheet.dart' show ScrollableBottomSheet;
import '/ui/widgets/toast.dart' show Card3ToastUtil;

class ActivateCardSheet extends ConsumerWidget {
  const ActivateCardSheet({
    super.key,
    required this.card,
    required this.params,
  });

  final CardInfoBasicInactivated card;
  final CardActivateParams params;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ScrollableBottomSheet(
      title: 'New item detected',
      sliversBuilder: (context) => [
        SliverFillRemaining(
          hasScrollBody: false,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              spacing: 24.0,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Flexible(
                  child: _CardCover(
                    card: card,
                    height: 200.0,
                  ),
                ),
                const Text(
                  'To be activated',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
      bottomBuilder: (context) => ThemeTextButton(
        onPressed: () => AppLoading.run(() async {
          try {
            // 调用API激活卡片
            await ref
                .read(apiServiceProvider)
                .activeCardByNfc(
                  uid: params.uid,
                  ctr: params.ctr ?? '',
                  cmac: params.cmac ?? '',
                  activateCode: params.activateCode,
                );
            ref.invalidate(fetchMyCardsProvider);
            Card3ToastUtil.showToast(message: ToastMessages.cardActivatedSuccessfully);
            if (context.mounted) {
              Navigator.of(context).maybePop();
            }
          } on ApiException catch (e) {
            Card3ToastUtil.showToast(message: ToastMessages.activationError('${e.message} ${e.code}'));
            rethrow;
          } catch (e) {
            Card3ToastUtil.showToast(
              message: ToastMessages.activationError(
                isNetworkError(e) ? context.l10nME.networkError : '$e',
              ),
            );
            rethrow;
          }
        }),
        text: 'Activate Now',
      ),
    );
  }
}

class _CardCover extends StatelessWidget {
  const _CardCover({
    required this.card,
    this.height,
  });

  final CardInfoBasicInactivated card;
  final double? height;

  @override
  Widget build(BuildContext context) {
    final paddedId = card.id.toString().padLeft(8, '0');
    final fit = BoxFit.cover;
    return Column(
      spacing: 16.0,
      mainAxisSize: MainAxisSize.min,
      children: [
        // 卡片图像容器
        Flexible(
          child: Container(
            width: double.infinity,
            height: height,
            alignment: Alignment.center,
            child: DecoratedBox(
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(12.0)),
                boxShadow: [
                  BoxShadow(
                    color: Color(0x20000000),
                    blurRadius: 10,
                    offset: Offset(0, 5),
                  ),
                ],
              ),
              child: MEImage(
                card.backCover,
                borderRadius: BorderRadius.circular(12.0),
                fit: fit,
                emptyBuilder: (context) => switch (card.cardType) {
                  CardType.STICKER => Assets.icons.images.stickerCover.image(fit: fit),
                  CardType.WRISTBAND => Assets.icons.images.wristbandCover.image(fit: fit),
                  _ => Assets.icons.images.normalBackcover.image(fit: fit),
                },
              ),
            ),
          ),
        ),
        // 卡片编号
        Text(
          '${paddedId.substring(0, 4)} ${paddedId.substring(paddedId.length - 4)}',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
      ],
    );
  }
}
