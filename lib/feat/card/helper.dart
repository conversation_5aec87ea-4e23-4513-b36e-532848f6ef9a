import 'package:me_misc/me_misc.dart' show MENavigatorExtension, globalL10nME, meNavigator;
import 'package:me_utils/me_utils.dart' show LogUtil;

import '/feat/link/helper.dart' show AppLinkHelper;
import '/internals/riverpod.dart' show globalContainer;
import '/models/card.dart' show CardInfoBasicInactivated, CardInfoBasicActivated;
import '/provider/api.dart' show ApiException, apiServiceProvider;
import '/routes/card3_routes.dart';
import '/ui/widgets/app_loading.dart';
import '/ui/widgets/dialog/scrollable_bottom_sheet.dart' show ScrollableBottomSheet;
import '/ui/widgets/toast.dart';
import 'activate_sheet.dart';

abstract final class CardHelper {
  static const _tag = '🪪 CardHelper';

  static const codeRegexRaw = r'\w{6,20}';
  static final codeRegex = RegExp(codeRegexRaw);

  // 处理卡片激活和跳转事件
  static bool handleCardActivation(
    String url,
  ) {
    if (!isCard3Format(url)) {
      Card3ToastUtil.showToast(
        message: ToastMessages.invalidCardFormat,
        duration: const Duration(seconds: 3),
      );
      return false;
    }

    final params = extractActivateParams(url);
    if (params == null) {
      return false;
    }

    AppLoading.run(() async {
      try {
        final card = await globalContainer
            .read(apiServiceProvider)
            .getCardInfoBasic(formalizedUri: params.formalizedUri);

        if (card.redirectUrl case final url when url.isNotEmpty) {
          await AppLinkHelper.handleUri(Uri.parse(url));
          return;
        }

        switch (card) {
          case CardInfoBasicActivated():
            meNavigator.removeNamedAndPushAndRemoveUntil(
              Routes.socialProfile.name,
              arguments: Routes.socialProfile.d(code: card.referralCode),
              predicate: (_) => true,
            );
          case CardInfoBasicInactivated():
            CardHelper.showActivateCardSheet(card: card, params: params);
        }
      } on ApiException catch (e) {
        Card3ToastUtil.showToast(message: '${e.message} ${e.code}');
        rethrow;
      } catch (e) {
        Card3ToastUtil.showToast(message: '${globalL10nME.exceptionError}\n$e');
        rethrow;
      }
    });
    return true;
  }

  // 显示激活卡片弹窗
  static void showActivateCardSheet({
    required CardInfoBasicInactivated card,
    required CardActivateParams params,
  }) {
    ScrollableBottomSheet.show(
      builder: (context) => ActivateCardSheet(card: card, params: params),
      heightFactor: 0.7,
    );
  }

  /// 检查扫描内容是否符合Card3 NFC格式
  /// 支持两种格式：
  /// 1. /?uid=044C5FD2151990&ctr=00007C&cmac=6AC65BEA2E09D7E5
  /// 2. /HXAODe 或 /044C5FD2151990
  static bool isCard3Format(String content) {
    final uri = formalizeUrl(content);
    if (uri == null) {
      return false;
    }

    if (!AppLinkHelper.allowedHosts.contains(uri.host) && uri.scheme != AppLinkHelper.scheme) {
      return false;
    }

    final pathFirst = uri.pathSegments.firstOrNull?.trim();

    // 忽略社交链接
    if (pathFirst == 'profile') {
      return false;
    }

    // 匹配第一种格式：t.card3.co/?uid=... 或 card3.co/?uid=...
    if (uri.queryParameters['uid']?.trim() case final uid? when uid.isNotEmpty) {
      return true;
    }

    // 匹配第二种格式：card3.co/?card_code=...&active_code=...
    if (uri.queryParameters['card_code']?.trim() case final cardCode? when cardCode.isNotEmpty) {
      return true;
    }

    // 匹配第三种格式：card3.co/...
    if (pathFirst case final path? when codeRegex.hasMatch(path)) {
      return true;
    }

    return false;
  }

  /// 从Card3 NFC内容中提取用于激活卡的标识符
  static CardActivateParams? extractActivateParams(String content) {
    final uri = formalizeUrl(content);
    if (uri == null) {
      return null;
    }

    final pathFirst = uri.pathSegments.firstOrNull?.trim();

    if (pathFirst == 'profile') {
      return null;
    }

    if (uri.queryParameters['uid']?.trim() case final uid? when uid.isNotEmpty) {
      final ctr = uri.queryParameters['ctr']?.trim();
      final cmac = uri.queryParameters['cmac']?.trim();
      return CardActivateParams.fromNfc(formalizedUri: uri, uid: uid, ctr: ctr, cmac: cmac);
    }

    if (uri.queryParameters['card_code']?.trim() case final cardCode? when cardCode.isNotEmpty) {
      final activateCode = uri.queryParameters['active_code']?.trim();
      return CardActivateParams.fromActiveCode(formalizedUri: uri, uid: cardCode, activateCode: activateCode);
    }

    if (codeRegex.firstMatch(uri.pathSegments.firstOrNull?.trim() ?? '')?.group(0) case final match?) {
      return CardActivateParams.fromUid(formalizedUri: uri, uid: match);
    }

    return null;
  }

  static Uri? formalizeUrl(String content) {
    content = content
        .trim()
        .replaceAll(RegExp(r'^(text|uri):(%20|\s)*', caseSensitive: false), '')
        .replaceAll(RegExp(r'\s|%0A$', caseSensitive: false), '');

    // Decode URL component if it matches the pattern.
    if (content.startsWith(RegExp(r'(\w+)%3A%2F%2F', caseSensitive: false))) {
      try {
        content = Uri.decodeComponent(content);
      } catch (_) {
        LogUtil.w('Failed to decode URL: $content', tag: _tag, tagWithTrace: false);
      }
    }

    content = content.trim();

    // Add scheme if missing.
    if (!content.startsWith(RegExp(r'\w+://', caseSensitive: false))) {
      content = 'https://$content';
    }

    return Uri.tryParse(content);
  }
}

class CardActivateParams {
  const CardActivateParams._({
    required this.formalizedUri,
    required this.uid,
    this.ctr,
    this.cmac,
    this.activateCode,
  });

  factory CardActivateParams.fromUid({
    required Uri formalizedUri,
    required String uid,
  }) {
    return CardActivateParams._(formalizedUri: formalizedUri, uid: uid);
  }

  factory CardActivateParams.fromActiveCode({
    required Uri formalizedUri,
    required String uid,
    required String? activateCode,
  }) {
    return CardActivateParams._(formalizedUri: formalizedUri, uid: uid, activateCode: activateCode);
  }

  factory CardActivateParams.fromNfc({
    required Uri formalizedUri,
    required String uid,
    required String? ctr,
    required String? cmac,
  }) {
    return CardActivateParams._(formalizedUri: formalizedUri, uid: uid, ctr: ctr, cmac: cmac);
  }

  final Uri formalizedUri;
  final String uid;
  final String? ctr;
  final String? cmac;
  final String? activateCode;

  @override
  String toString() => 'CardParams(uid: $uid, ctr: $ctr, cmac: $cmac, activateCode: $activateCode)';
}
