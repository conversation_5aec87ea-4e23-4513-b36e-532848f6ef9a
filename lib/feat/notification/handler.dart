import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart' show kReleaseMode;
import 'package:me_misc/me_misc.dart' show MENavigatorExtension, meNavigator, meNavigatorKey;
import 'package:me_models/me_models.dart' show NotificationParams;
import 'package:me_utils/me_utils.dart' show LogUtil;

import '/constants/envs.dart' show isSealed;
import '/models/business.dart' show ImageAITaskDone;
import '/models/user.dart' show UserFromRelationType;
import '/routes/card3_routes.dart' show Routes;

enum NotificationParamsAction {
  openNotificationCenter('openNotificationCenter'),
  openConnections('openConnections'),
  openAIGenerateImage('openAIGenerateImage');

  const NotificationParamsAction(this.value);

  final String value;

  static NotificationParamsAction? fromValue(String? value) {
    if (value == null || value.isEmpty) {
      return null;
    }
    return NotificationParamsAction.values.firstWhereOrNull(
      (e) => e.value == value,
    );
  }
}

bool handleNotificationOpenWithPayload(Object value) {
  const tag = '🔔 HandleNotificationOpenWithPayload';
  LogUtil.dd(
    () => '$value',
    tag: tag,
    tagWithTrace: false,
    report: !isSealed && kReleaseMode,
  );

  final Map<String, dynamic>? parsedPayload = switch (value) {
    final String p when p.trim().isNotEmpty => jsonDecode(p.trim()),
    final Map p => p,
    _ => null,
  };
  if (parsedPayload == null || parsedPayload.isEmpty) {
    return false;
  }

  final Map<String, dynamic>? effectivePayload = switch (parsedPayload) {
    {'params': {'app': final Map appParams}} => appParams.cast(),
    _ => null,
  };
  if (effectivePayload == null || effectivePayload.isEmpty) {
    return false;
  }

  final params = NotificationParams.fromJson(effectivePayload);
  final action = NotificationParamsAction.fromValue(params.nativeAction);
  if (action != null) {
    switch (action) {
      case NotificationParamsAction.openNotificationCenter:
        _handleOpenNotificationCenter();
      case NotificationParamsAction.openConnections:
        final type = UserFromRelationType.fromName(
          params.nativeActionArguments?['type'] as String?,
        );
        _handleOpenConnections(type);
      case NotificationParamsAction.openAIGenerateImage:
        final arguments = params.nativeActionArguments;
        if (arguments == null) {
          return false;
        }
        final taskResult = ImageAITaskDone.fromJson(arguments);
        _handleOpenAIGenerateImage(taskResult);
    }
    return true;
  }

  if (params.navigationRoute case final route?) {
    if (meNavigatorKey.currentState == null) {
      LogUtil.w(
        'Navigator is not ready for navigations.',
        tag: tag,
        tagWithTrace: false,
      );
      return false;
    }
    if (params.navigationRouteReplaceExists) {
      meNavigator.removeNamedAndPushAndRemoveUntil(
        route,
        arguments: params.navigationArguments,
        predicate: (_) => true,
      );
    } else {
      meNavigator.pushNamed(
        route,
        arguments: params.navigationArguments,
      );
    }
    return true;
  }

  return false;
}

// ServiceApi get _service => globalContainer.read(apiServiceProvider);

void _handleOpenNotificationCenter() {
  meNavigator.removeNamedAndPushAndRemoveUntil(
    Routes.notification.name,
    predicate: (_) => true,
  );
}

void _handleOpenConnections(UserFromRelationType? type) {
  meNavigator.removeNamedAndPushAndRemoveUntil(
    Routes.funConnection.name,
    arguments: Routes.funConnection.d(type: type),
    predicate: (_) => true,
  );
}

Future<void> _handleOpenAIGenerateImage(ImageAITaskDone taskResult) async {
  final result = await meNavigator.pushNamed(
    Routes.aiGenerateImage.name,
    arguments: Routes.aiGenerateImage.d(
      taskId: taskResult.taskId,
      generatedUrl: taskResult.imageUrl,
    ),
  );
  if (result is! String) {
    return;
  }
  meNavigator.pushNamed(
    Routes.socialEditProfile.name,
    arguments: Routes.socialEditProfile.d(pendingAvatarUrl: result),
  );
}
