import 'package:flutter/widgets.dart' show WidgetsBinding;
import 'package:me_misc/me_misc.dart' show MENavigatorExtension, meNavigator, meNavigatorObserver, postRun;

import '/constants/envs.dart';
import '/feat/card/helper.dart' show CardHelper;
import '/internals/methods.dart' show handleExceptions;
import '/internals/riverpod.dart' show globalContainer;
import '/models/card.dart' show CardInfoBasicActivated, CardInfoBasicInactivated;
import '/provider/api.dart' show apiServiceProvider;
import '/provider/user.dart' show userReferralFromProvider;
import '/routes/card3_routes.dart' show Routes;
import '/ui/widgets/app_loading.dart';
import '/ui/widgets/toast.dart';
import 'helper.dart' show AppLinkHelper;

abstract class AppLinkHandler {
  const AppLinkHandler();

  bool onLink(Uri uri);
}

class SchemeLinkHandler extends AppLinkHandler {
  const SchemeLinkHandler();

  static const methodActivateCard = 'active_card';
  static const methodCheckin = 'checkin';
  static const methodCustomize = 'customize';
  static const methodEvent = 'event';
  static const methodProfile = 'profile';
  static const methodReferral = 'referral';
  static const methodWebview = 'webview';
  static const methods = [
    methodActivateCard,
    methodCheckin,
    methodCustomize,
    methodEvent,
    methodProfile,
    methodReferral,
    methodWebview,
  ];

  static bool _validateUri(Uri uri) {
    return uri.scheme == AppLinkHelper.scheme && methods.contains(uri.pathSegments.elementAtOrNull(0));
  }

  @override
  bool onLink(Uri uri) {
    if (!_validateUri(uri)) {
      return false;
    }

    final path = uri.pathSegments.firstOrNull;
    switch (path) {
      case methodCustomize:
        return _handleCustomize(uri);
      case methodProfile:
        return _handleProfile(uri);
      case methodCheckin:
      case methodEvent:
      case methodWebview:
        return _handleWebview(uri);
      case methodReferral:
        return _handleReferral(uri);
      default:
        return _handleActiveCard(uri);
    }
  }

  static bool _handleActiveCard(Uri uri) {
    if (CardHelper.isCard3Format(uri.toString())) {
      return CardHelper.handleCardActivation(uri.toString());
    }
    return false;
  }

  static bool _handleCustomize(Uri uri) {
    final code = uri.queryParameters['code'];
    if (code == null) {
      return false;
    }
    postRun(() {
      meNavigator.removeNamedAndPushAndRemoveUntil(
        Routes.customize.name,
        arguments: Routes.customize.d(code: code),
        predicate: (_) => true,
      );
    });
    return true;
  }

  static bool _handleProfile(Uri uri) {
    return const SocialLinkHandler().onLink(uri);
  }

  static bool _handleWebview(Uri uri) {
    final url = (uri.queryParameters['url'] ?? '').trim();
    final title = (uri.queryParameters['title'] ?? '').trim();
    if (url.isEmpty) {
      return false;
    }

    late final String decodedUrl, decodedTitle;
    try {
      decodedUrl = Uri.decodeComponent(url);
      decodedTitle = Uri.decodeComponent(title);
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      return false;
    }

    final parsedUrl = Uri.tryParse(decodedUrl);
    if (parsedUrl == null || !parsedUrl.hasAbsolutePath) {
      return false;
    }

    meNavigator.removeNamedAndPushAndRemoveUntil(
      Routes.webview.name,
      arguments: Routes.webview.d(
        url: parsedUrl.toString(),
        title: decodedTitle.isNotEmpty ? decodedTitle : null,
      ),
      predicate: (_) => true,
    );
    return true;
  }

  static bool _handleReferral(Uri uri) {
    return const ReferralLinkHandler().onLink(uri);
  }
}

class FunLinkHandler extends AppLinkHandler {
  const FunLinkHandler();

  @override
  bool onLink(Uri uri) {
    if (uri.host != envUrlCard3.replaceFirst(RegExp(r'https?://'), '')) {
      return false;
    }

    final pathFirst = uri.pathSegments.elementAtOrNull(0);
    switch (pathFirst) {
      case SchemeLinkHandler.methodCheckin:
      case SchemeLinkHandler.methodEvent:
        final title = uri.queryParameters['title']?.trim() ?? '';
        postRun(() {
          meNavigator.removeNamedAndPushAndRemoveUntil(
            Routes.webview.name,
            arguments: Routes.webview.d(
              url: uri.toString(),
              title: title.isNotEmpty ? title : null,
            ),
            predicate: (_) => true,
          );
        });
        return true;
      case SchemeLinkHandler.methodCustomize:
        final code = uri.queryParameters['code'];
        if (code == null) {
          return false;
        }
        postRun(() {
          meNavigator.removeNamedAndPushAndRemoveUntil(
            Routes.customize.name,
            arguments: Routes.customize.d(code: code),
            predicate: (_) => true,
          );
        });
        return true;
      default:
        return CardHelper.handleCardActivation(uri.toString());
    }
  }
}

class ShortLinkHandler extends AppLinkHandler {
  const ShortLinkHandler();

  static Iterable<String> get _allowedHosts => [
    envUrlShort,
    envUrlShortApp,
  ].map((e) => e.replaceFirst(RegExp(r'https?://'), ''));

  @override
  bool onLink(Uri uri) {
    if (!_allowedHosts.contains(uri.host)) {
      return false;
    }

    final url = uri.toString();

    final params = CardHelper.extractActivateParams(url);
    final code = CardHelper.codeRegex.firstMatch(url)?.group(0);
    if (params == null && code == null) {
      return false;
    }

    if (params != null) {
      return CardHelper.handleCardActivation(url);
    }

    if (code != null) {
      meNavigator.removeNamedAndPushAndRemoveUntil(
        Routes.socialProfile.name,
        arguments: Routes.socialProfile.d(code: code),
        predicate: (_) => true,
      );
      return true;
    }

    return false;
  }
}

class SocialLinkHandler extends AppLinkHandler {
  const SocialLinkHandler();

  static String? _extractCodeFromUrl(Uri uri) {
    String? code;
    final url = uri.toString();
    if ((uri.scheme == AppLinkHelper.scheme || url.startsWith(envUrlSocial)) &&
        uri.pathSegments.elementAtOrNull(0) == 'profile') {
      code = CardHelper.codeRegex.firstMatch(uri.queryParameters['card_code'] ?? '')?.group(0);
    }
    if (code?.trim().isEmpty ?? false) {
      code = null;
    }
    return code;
  }

  @override
  bool onLink(Uri uri) {
    final code = _extractCodeFromUrl(uri);
    if (code == null) {
      return false;
    }
    meNavigator.removeNamedAndPushAndRemoveUntil(
      Routes.socialProfile.name,
      arguments: Routes.socialProfile.d(code: code),
      predicate: (_) => true,
    );
    return true;
  }
}

class ReferralLinkHandler extends AppLinkHandler {
  const ReferralLinkHandler();

  String? _extractCodeFromUrl(Uri uri) {
    final url = uri.toString();
    if (url.startsWith(envUrlCard3)) {
      return uri.queryParameters['referral'];
    }
    return null;
  }

  @override
  bool onLink(Uri uri) {
    final code = _extractCodeFromUrl(uri);
    if (code == null) {
      return false;
    }

    if (!meNavigatorObserver.history.any((route) => route.settings.name == Routes.home.name)) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        globalContainer.read(userReferralFromProvider.notifier).state = code;
      });
      return true;
    }

    AppLoading.run(() async {
      final card = await globalContainer.read(apiServiceProvider).getCardInfoBasic(formalizedUri: uri);

      if (card.redirectUrl case final url when url.isNotEmpty) {
        await AppLinkHelper.handleUri(Uri.parse(url));
        return;
      }

      switch (card) {
        case CardInfoBasicActivated():
          meNavigator.removeNamedAndPushAndRemoveUntil(
            Routes.socialProfile.name,
            arguments: Routes.socialProfile.d(code: card.referralCode),
            predicate: (_) => true,
          );
        case CardInfoBasicInactivated():
          Card3ToastUtil.showToast(message: ToastMessages.cardNotActivated);
      }
    });
    return true;
  }
}
