import 'dart:async';

import 'package:app_links/app_links.dart';
import 'package:me_misc/me_misc.dart';
import 'package:me_utils/me_utils.dart' show LogUtil;
import 'package:url_launcher/url_launcher.dart' show canLaunchUrl, launchUrl, LaunchMode;

import '/constants/envs.dart';
import '/internals/methods.dart' show handleExceptions;
import '/routes/card3_routes.dart' show Routes;

import 'handler.dart';

abstract class AppLinkHelper {
  static const _tag = '🔗 AppLinkHelper';

  static const scheme = 'card3';
  static final _$ = AppLinks();

  static final _handlers = <AppLinkHandler>[
    const SchemeLinkHandler(),
    const ReferralLinkHandler(),
    const FunLinkHandler(),
    const SocialLinkHandler(),
    const ShortLinkHandler(),
  ];

  static StreamSubscription<Uri>? _sub;
  static bool _startHandle = false;

  static Uri? pendingLink;

  static Iterable<String> get allowedHosts => [
    envUrlCard3,
    envUrlSocial,
    envUrlShort,
    envUrlShortApp,
  ].map((e) => Uri.parse(e).host);

  static void register() {
    _sub?.cancel();
    _sub = _$.uriLinkStream.listen(handleUri);
    meNavigatorObserver.addListener(
      MERouteListener(
        actions: MERouteAction.values.toSet(),
        onRouteChanged: (_, newRoute, _) {
          final previousStartHandle = _startHandle;
          _startHandle =
              newRoute?.settings.name == Routes.home.name ||
              meNavigatorObserver.history.any((r) => r.settings.name == Routes.home.name);
          if (pendingLink case final link? when _startHandle && !previousStartHandle) {
            handleUri(link);
          }
        },
      ),
    );
    _fetchInitialLink();
  }

  static void _fetchInitialLink() {
    _$.getInitialLink().then(
      (link) {
        if (link != null) {
          LogUtil.d(
            'Initial link: $link',
            tag: _tag,
            tagWithTrace: false,
          );
          pendingLink ??= link;
        }
      },
      onError: (e, s) {
        handleExceptions(error: e, stackTrace: s);
      },
    );
  }

  static void addHandler(AppLinkHandler handler) {
    _handlers.add(handler);
  }

  static void removeHandler(AppLinkHandler handler) {
    _handlers.remove(handler);
  }

  static Future<bool> handleUri(Uri uri) async {
    if (!_startHandle) {
      LogUtil.d(
        'Pending AppLink: $uri',
        tag: _tag,
        tagWithTrace: false,
      );

      pendingLink = uri;
      return false;
    }

    LogUtil.d(
      'Incoming AppLink: $uri',
      tag: _tag,
      tagWithTrace: false,
    );

    Future<bool> launchIfApplicable() async {
      // Fallback to launch the URL with external application.
      if (await canLaunchUrl(uri)) {
        LogUtil.w(
          'Link handled by native launching\n$uri',
          tag: _tag,
          tagWithTrace: false,
        );
        launchUrl(uri, mode: LaunchMode.externalApplication);
        return true;
      }
      return false;
    }

    if (uri.scheme != AppLinkHelper.scheme && !allowedHosts.contains(uri.host)) {
      LogUtil.d(
        'Link is neither match the scheme (${AppLinkHelper.scheme}) nor in the allowed host list.'
        '\n$uri',
        tag: _tag,
        tagWithTrace: false,
      );
      return launchIfApplicable();
    }

    for (final handler in _handlers) {
      try {
        if (handler.onLink(uri)) {
          LogUtil.d(
            'Link handled by ${handler.runtimeType}\n$uri',
            tag: _tag,
            tagWithTrace: false,
          );
          return true;
        }
      } catch (e, s) {
        handleExceptions(error: e, stackTrace: s);
      }
    }

    if (await launchIfApplicable()) {
      return true;
    }

    LogUtil.w(
      'Link not handled\n$uri',
      tag: _tag,
      tagWithTrace: false,
    );
    return false;
  }

  // 清理资源
  static void dispose() {
    _sub?.cancel();
  }
}
