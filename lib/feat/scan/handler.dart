import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:me_utils/me_utils.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:solana/solana.dart' show Ed25519HDPublicKey;

import '/feat/link/handler.dart' show AppLinkHandler;
import '/feat/link/helper.dart' show AppLinkHelper;
import 'uni_qr.dart';

abstract class ScanHandler<T> implements ScanLifecycle {
  ScanHandler() {
    onInit();
  }

  @override
  @mustCallSuper
  void onInit() {}

  @override
  @mustCallSuper
  void onResume() {}

  @override
  @mustCallSuper
  void onPause() {}

  @override
  @mustCallSuper
  void dispose() {}

  FutureOr<ScanDat> accept(BarcodeCapture result) async {
    LogUtil.dd(() => result);
    for (final code in result.barcodes) {
      if (code.rawValue != null) {
        final r = await acceptSuccess(code.rawValue!.trim());
        if (r.state != ScanState.mismatched) {
          return r;
        }
      }
    }
    return ScanDat.mismatched();
  }

  FutureOr<ScanDat> acceptSuccess(String text);
}

class AppLinkScanHandler extends ScanHandler<String> {
  AppLinkScanHandler({this.handlers = const {}});

  final Set<AppLinkHandler> handlers;

  @override
  FutureOr<ScanDat> acceptSuccess(String text) async {
    final uri = Uri.tryParse(text);
    if (uri == null) {
      return ScanDat.mismatched();
    }

    if (handlers.isNotEmpty) {
      for (final handler in handlers) {
        if (handler.onLink(uri)) {
          return ScanDat.processed(text);
        }
      }
      return ScanDat.mismatched();
    }

    final handled = await AppLinkHelper.handleUri(uri);
    return handled ? ScanDat.processed(text) : ScanDat.mismatched();
  }
}

final class SolanaAddressScanHandler extends ScanHandler<String> {
  @override
  ScanDat acceptSuccess(String text) {
    if (Ed25519HDPublicKey.isValidFromBase58(text)) {
      return ScanDat.processed(text);
    } else {
      return ScanDat.mismatched();
    }
  }
}

/// ERC-681 compatible.
/// https://github.com/ethereum/ercs/blob/master/ERCS/erc-681.md
final class EthereumAddressScanHandler extends ScanHandler<String> {
  static final _addressRegex = RegExp(r'(0[xX][0-9a-fA-F]{40})');

  @override
  ScanDat acceptSuccess(String text) {
    final uri = Uri.tryParse(text);
    if (uri == null) {
      return ScanDat.mismatched();
    }

    String? match = _addressRegex.firstMatch(uri.scheme)?.group(1);
    if (match != null) {
      return ScanDat.processed(match);
    }

    match = _addressRegex.firstMatch(uri.pathSegments.firstOrNull ?? '')?.group(1);
    if (match != null) {
      return ScanDat.processed(match);
    }

    return ScanDat.mismatched();
  }
}
