import 'package:flutter/material.dart';
import 'package:me_constants/me_constants.dart' hide themeBy;
import 'package:me_constants/me_constants.dart' as c show themeBy;

import '/res/colors.gen.dart';
import '/res/fonts.gen.dart';

const METheme defaultMEThemeDark = METheme(
  brightness: Brightness.dark,
  themeColor: ColorName.themeColorDark,
  backgroundColor: ColorName.backgroundColorDark,
  cardColor: ColorName.cardColorDark,
  dividerColor: ColorName.dividerColorDark,
  iconColor: ColorName.iconColorDark,
  listColor: ColorName.listColorDark,
  primaryTextColor: ColorName.primaryTextColorDark,
  captionTextColor: ColorName.captionTextColorDark,
  blueGreyIconColor: ColorName.blueGreyIconColorDark,
  shimmerHighlightColor: ColorName.shimmerHighlightColorDark,
  successColor: ColorName.successColor,
  notificationColor: ColorName.notificationColor,
  borderRadius: BorderRadius.all(Radius.circular(16.0)),
  fontFamilyLatin: FontFamily.harmonyOSSans,
  fontHeightLatin: 1.2,
);

const METheme defaultMEThemeLight = METheme(
  brightness: Brightness.light,
  themeColor: ColorName.themeColorLight,
  backgroundColor: ColorName.backgroundColorLight,
  cardColor: ColorName.cardColorLight,
  dividerColor: ColorName.dividerColorLight,
  iconColor: ColorName.iconColorLight,
  listColor: ColorName.listColorLight,
  primaryTextColor: ColorName.primaryTextColorLight,
  captionTextColor: ColorName.captionTextColorLight,
  blueGreyIconColor: ColorName.blueGreyIconColorLight,
  shimmerHighlightColor: ColorName.shimmerHighlightColorLight,
  successColor: ColorName.successColor,
  notificationColor: ColorName.notificationColor,
  borderRadius: BorderRadius.all(Radius.circular(16.0)),
  fontFamilyLatin: FontFamily.harmonyOSSans,
  fontHeightLatin: 1.2,
);

ThemeData themeBy({
  required METheme meTheme,
  required Locale locale,
}) {
  ThemeData theme = c.themeBy(
    meTheme: meTheme,
    locale: locale,
  );
  theme = theme.copyWith(
    colorScheme: theme.colorScheme.copyWith(surface: meTheme.listColor),
    inputDecorationTheme: theme.inputDecorationTheme.copyWith(
      fillColor: meTheme.backgroundColor,
      enabledBorder: theme.inputDecorationTheme.enabledBorder?.copyWith(
        borderSide: BorderSide(color: theme.focusColor),
      ),
    ),
    bottomSheetTheme: theme.bottomSheetTheme.copyWith(
      modalBackgroundColor: meTheme.backgroundColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(10.0),
        ),
      ),
    ),
  );
  return theme;
}
